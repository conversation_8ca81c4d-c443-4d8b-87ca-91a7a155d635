/* Graph colors  */
body.theme-1 {
    --used-color: #0CAF60;
    /* Base Color */
    --used-color-medium: #7EDD81;
    /* Intermediate Shade */
    --used-color-light: #A5E6A2;
    /* Lighter Shade for Contrast */
    --used-color-dark: #009D4D;
    /* Darker Shade for Depth */
    --used-color-darker: #007A3D;
    /* Even Darker Shade */
    --used-color-contrast: #C7F7B5;
    /* Soft Contrast Shade */
}

body.theme-2 {
    --used-color: #584ED2;
    /* Base Color */
    --used-color-medium: #A3A3E5;
    /* Intermediate Shade */
    --used-color-light: #B7B4E5;
    /* Lighter Shade for Contrast */
    --used-color-dark: #402B9B;
    /* Darker Shade for Depth */
    --used-color-darker: #2E1F73;
    /* Even Darker Shade */
    --used-color-contrast: #D0D4F3;
    /* Soft Contrast Shade */
}

body.theme-3 {
    --used-color: #6FD943;
    /* Base Color */
    --used-color-medium: #A5E07D;
    /* Intermediate Shade */
    --used-color-light: #A8D99F;
    /* Lighter Shade for Contrast */
    --used-color-dark: #5BAF35;
    /* Darker Shade for Depth */
    --used-color-darker: #489A29;
    /* Even Darker Shade */
    --used-color-contrast: #D7F9A0;
    /* Soft Contrast Shade */
}

body.theme-4 {
    --used-color: #145388;
    /* Base Color */
    --used-color-medium: #6FA5B2;
    /* Intermediate Shade */
    --used-color-light: #A5C6D2;
    /* Lighter Shade for Contrast */
    --used-color-dark: #0B3E68;
    /* Darker Shade for Depth */
    --used-color-darker: #07304F;
    /* Even Darker Shade */
    --used-color-contrast: #A7D3E0;
    /* Soft Contrast Shade */
}

body.theme-5 {
    --used-color: #B9406B;
    /* Base Color */
    --used-color-medium: #D39A9B;
    /* Intermediate Shade */
    --used-color-light: #DCA2B1;
    /* Lighter Shade for Contrast */
    --used-color-dark: #9F2C5A;
    /* Darker Shade for Depth */
    --used-color-darker: #7A1F43;
    /* Even Darker Shade */
    --used-color-contrast: #EAB2C3;
    /* Soft Contrast Shade */
}

body.theme-6 {
    --used-color: #008ECC;
    /* Base Color */
    --used-color-medium: #5BB6E0;
    /* Intermediate Shade */
    --used-color-light: #A1D5E0;
    /* Lighter Shade for Contrast */
    --used-color-dark: #0076A1;
    /* Darker Shade for Depth */
    --used-color-darker: #005D7C;
    /* Even Darker Shade */
    --used-color-contrast: #A4D2E8;
    /* Soft Contrast Shade */
}

body.theme-7 {
    --used-color: #922C88;
    /* Base Color */
    --used-color-medium: #B878B1;
    /* Intermediate Shade */
    --used-color-light: #D7A1C5;
    /* Lighter Shade for Contrast */
    --used-color-dark: #7A2270;
    /* Darker Shade for Depth */
    --used-color-darker: #5D1A55;
    /* Even Darker Shade */
    --used-color-contrast: #E2B1D6;
    /* Soft Contrast Shade */
}

body.theme-8 {
    --used-color: #C0A145;
    /* Base Color */
    --used-color-medium: #D6C682;
    /* Intermediate Shade */
    --used-color-light: #E3DAB1;
    /* Lighter Shade for Contrast */
    --used-color-dark: #A38B34;
    /* Darker Shade for Depth */
    --used-color-darker: #8B7028;
    /* Even Darker Shade */
    --used-color-contrast: #E9E3B2;
    /* Soft Contrast Shade */
}

body.theme-9 {
    --used-color: #48494B;
    /* Base Color */
    --used-color-medium: #A7A8A9;
    /* Intermediate Shade */
    --used-color-light: #AFAFAF;
    /* Lighter Shade for Contrast */
    --used-color-dark: #3B3C3E;
    /* Darker Shade for Depth */
    --used-color-darker: #2E2F30;
    /* Even Darker Shade */
    --used-color-contrast: #B3B3B3;
    /* Soft Contrast Shade */
}

body.theme-10 {
    --used-color: #0C7785;
    /* Base Color */
    --used-color-medium: #6FA1A8;
    /* Intermediate Shade */
    --used-color-light: #A4D2D5;
    /* Lighter Shade for Contrast */
    --used-color-dark: #095E68;
    /* Darker Shade for Depth */
    --used-color-darker: #074B54;
    /* Even Darker Shade */
    --used-color-contrast: #A8D7DB;
    /* Soft Contrast Shade */
}

/* pinnd */
html[data-behaviour="pinned"] .dash-sidebar {
    width: 100px !important;
    transition: width 0.3s ease;
    /* Smooth transition for width */
}

html[data-behaviour="pinned"] .dash-item .dash-mtext,
html[data-behaviour="pinned"] .dash-item .dash-arrow,
html[data-behaviour="pinned"] .nav_actions_bar,
html[data-behaviour="pinned"] .navbar-footer,
html[data-behaviour="pinned"] .m-header {
    display: none;
    opacity: 0;
    /* Initially set opacity to 0 */
    transition: opacity 0.3s ease;
    /* Smooth transition for opacity */
}

html[data-behaviour="pinned"] .dash-container {
    margin-left: 8% !important;
}

html[data-behaviour="pinned"] .dash-header {
    left: 9%;
}

html[data-behaviour="pinned"] .dash-link {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

html[data-behaviour="pinned"] .dash-link .dashdash-micon {
    margin-right: 0 !important;
}

/* Expanded styles on hover */
/* hidding logo on pinned */


html[data-behaviour="pinned"] .dash-sidebar:hover {
    width: 250px !important;

}

html[data-behaviour="pinned"] .dash-sidebar:hover .dash-link {
    justify-content: start !important;

}

/* Styles for showing dash items when hovered */
html[data-behaviour="pinned"] .dash-sidebar:hover .dash-item .dash-mtext,
html[data-behaviour="pinned"] .dash-sidebar:hover .dash-item .dash-arrow,
html[data-behaviour="pinned"] .dash-sidebar:hover .nav_actions_bar,
html[data-behaviour="pinned"] .dash-sidebar:hover .navbar-footer,
html[data-behaviour="pinned"] .m-header,
html[data-behaviour="pinned"] .dash-link {
    display: block;
    opacity: 1;
    margin-top: 0px;
    .dash-arrow{
        position: absolute;
        right: 10px;
    }
    
}

/* Initially hide the main logo with opacity */
html[data-behaviour="pinned"] .dash-sidebar .main-logo a img {
    opacity: 0;
}

/* Show the main logo with opacity transition on hover */
html[data-behaviour="pinned"] .dash-sidebar:hover .main-logo a img {
    opacity: 1;
    transition: opacity 0.7s ease-in; /* Smooth transition with no delay */
}
.dash-sidebar .main-logo a #fa {
    opacity: 0;
    margin-top: 50px;
}
html[data-behaviour="pinned"] .dash-sidebar:hover .main-logo a #fa {
    display: none; /* Ensure it's visible initially */
}
html[data-behaviour="pinned"] .dash-sidebar .main-logo a #fa{
    opacity: 1;
    position: absolute;
    top: 10px;
    left: 15px;
    margin-top: 0px;
    width: 70% !important;
    height: 100px !important;
} 

html[data-behaviour="pinned"] .dash-sidebar .dash-navbar a{
    margin-left: 25px !important;
    max-width: 45px;
    max-height: 40px;
} 
html[data-behaviour="pinned"] .dash-sidebar:hover  .dash-navbar a{
    margin-left: 15px !important;
    margin-right: 15px !important;
    max-width: 100% !important;
    max-height: auto !important;
} 

html[data-behaviour="pinned"] .dash-navbar a span{
    margin-right: -4px;
} 


html[data-behaviour="pinned"] .dash-sidebar .dash-submenu{
    height:0px !important;
    overflow: hidden !important;
    margin-bottom: 0px !important;
}
html[data-behaviour="pinned"] .dash-sidebar:hover .dash-submenu{
    height: 100% !important;
    overflow:visible !important;
}
/* Ensure opacity transitions back when not hovered */
html[data-behaviour="pinned"] .dash-sidebar .dash-item .dash-mtext,
html[data-behaviour="pinned"] .dash-sidebar .dash-item .dash-arrow,
html[data-behaviour="pinned"] .dash-sidebar .nav_actions_bar,
html[data-behaviour="pinned"] .dash-sidebar .navbar-footer {
    opacity: 0;
    /* Set opacity back to 0 when not hovered */
    transition: opacity 0.3s ease;
    /* Smooth transition for opacity */
    white-space: nowrap !important;
    overflow: hidden !important;
}


/* ****************************************chnages ********************************************* */

.Permission {
    white-space: inherit !important;
}

.action-btn{
    width: 29px;
    height: 28px;
    border-radius: 9.3552px;
    color: #fff;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    flex-shrink: 0;
}

.repeater-action-btn {
    width: 23px;
    height: 23px;
    border-radius: 9.3552px;
    color: #fff;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    flex-shrink: 0;
}
.delete-form-btn{
    display: inline;
}

.dash-sidebar .main-logo {
    justify-content: center;
    /*height: 100%;*/
    min-height: 80px;
    max-height: 80px;
    width: 100%;
    min-width: 255px;
    /*max-width: 255px;*/
}
/*a.b-brand {*/
/*    height: 100%;*/
/*    width: 100%;*/
/*}*/
.dash-sidebar .main-logo a img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    width: auto !important;
    height: auto;
    max-width: -webkit-fill-available !important;
    max-height: -webkit-fill-available !important;
    max-width: -moz-available;
    max-height: -moz-available;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    vertical-align: middle;
}

.price-card {
    text-align: center;
    position: relative;
    margin-top: 30px;
    height: 100%;
    max-height: 510px;

}
.price-card.price-2 {
    color: #fff;
}
.price-card.price-2 .price-badge {
    color: #fff;
    background: #1C232F;
}
.price-card .p-price {
    font-size: 80px;
}
.price-card .price-badge {
    color: #fff;
    padding: 7px 24px;
    border-radius: 30px;
    position: absolute;
    top: 0;
    left: 50%;
    transform: translate(-50%, -50%);
}
.price-card .list-unstyled {
    display: inline-block;
}
.price-card .list-unstyled li {
    display: flex;
    align-items: center;
}
.price-card .list-unstyled li + li {
    margin-top: 8px;
}
.price-card .list-unstyled .theme-avtar {
    display: inline-flex;
    width: 30px;
    height: 30px;
    border-radius: 10px;
    background: #008ECC;
    margin-right: 15px;
}
.side-feature {
    overflow: hidden;
}
.faq .accordion .accordion-item {
    border-radius: 10px;
    overflow: hidden;
    border: none;
    margin-bottom: 10px;
}

.faq .accordion .accordion-item .accordion-button {
    font-weight: 500;
    padding: 1.3rem 1.25rem;
}

.faq .accordion .accordion-item .accordion-button span > i {
    font-size: 20px;
    margin-right: 8px;
}

.faq .accordion .accordion-item .accordion-button:not(.collapsed) {
    border-radius: 10px;
    background: transparent;
    box-shadow: 0 6px 30px rgba(182, 186, 203, 0.3);
}

.faq .accordion .accordion-item .accordion-body {
    /*padding: 2.3rem 2.3rem 2.3rem 3rem;*/
    padding: 1rem 1.25rem;
}

.choose-files div {
    color: #fff;
    background: #584ED2 !important;
    border: none;
    border-radius: 10px;
    padding: 8px 15px;
    max-width: 155px !important;
    font-size: 12px;
    font-weight: 500;
}
.file {
    position: relative !important;
    left: 0;
    opacity: 0;
    top: 0;
    bottom: 0;
    width: 80%;
    border: none;
    padding: 0;
    margin: 0;
    cursor: pointer;
}
.file-icon {
    width: 30px;
    height: 30px;
    background: #0F5EF7;
    border-radius: 50px;
    float: left;
    text-align: center;
}
.file-icon i {
    color: #fff;
    z-index: 9999;
    position: relative;
    font-size: 14px;
}
.first-file {
    width: 100%;
    float: left;
    padding-bottom: 20px;
    position: relative;
}
.file-des {
    width: calc(100% - 40px);
    float: right;
    color: #A3AFBB;
    font-size: 12px;
}
.file-des span {
    width: 100%;
    float: left;
    color: #011C4B;
}
.general-tab .column-card {
    flex-direction: column;
}
.first-file:before {
    position: absolute;
    bottom: 0;
    width: 3px;
    height: 100%;
    background: var(--bs-primary) !important;
    content: "";
    left: 25px;
}
.first-file:last-child:before {
    background: none;
}
.setting-favimg{
    width: 100px;
}
.setting-logoimg{
    width: 200px;
}
.colorinput {
    margin: 0;
    position: relative;
    cursor: pointer;
}

.colorinput-input {
    position: absolute;
    z-index: -1;
    opacity: 0;
}

.colorinput-color {
    background-color: #fdfdff;
    border-color: #e4e6fc;
    border-width: 1px;
    border-style: solid;
    display: inline-block;
    width: 1.75rem;
    height: 1.75rem;
    border-radius: 3px;
    color: #fff;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.colorinput-color:before {
    content: '';
    opacity: 0;
    position: absolute;
    top: .25rem;
    left: .25rem;
    height: 1.25rem;
    width: 1.25rem;
    transition: .3s opacity;
    background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E") no-repeat center center/50% 50%;
}

.colorinput-input:checked~.colorinput-color:before {
    opacity: 1;
}

.img_setting {
    filter: drop-shadow(2px 3px 7px #011C4B);
}
.btn-apply
{
    font-size: 31px;
}
.avatar {
    text-align: center;
    border-radius: 100%;
    overflow: hidden;
    background-color: #eee;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center center;
}
.avatar-sm {
    width: 2.4375rem;
    height: 2.4375rem;
    font-size: 0.75rem;
    border-radius: 0.2rem;
}

.avatar {
    position: relative;
    color: #FFF;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    vertical-align: middle;
    font-size: 1rem;
    font-weight: 600;
    height: 3.125rem;
    width: 3.125rem;
    border-radius: 0.25rem;
}

.avatar img {
    width: 100%;
    border-radius: 0.25rem;
}

.avatar.rounded-circle img {
    border-radius: 50%;
}

/*.avatar span {*/
/*    background-color: #051C4B;*/
/*}*/

.avatar + .avatar {
    margin-left: .25rem;
}

.avatar + .avatar-content {
    display: inline-block;
    margin-left: .75rem;
}

.avatar-xl {
    width: 6rem;
    height: 6rem;
    font-size: 1.375rem;
}

.avatar-lg {
    width: 4rem;
    height: 4rem;
    font-size: 1.25rem;
}

.avatar-sm {
    width: 2.4375rem;
    height: 2.4375rem;
    font-size: 0.75rem;
    border-radius: 0.2rem;
}

.avatar-group {
    display: inline-block;
    line-height: 1;
}

.avatar-group .avatar {
    z-index: 1;
    transition: margin 0.15s ease-in-out;
}

@media (prefers-reduced-motion: reduce) {
    .avatar-group .avatar {
        transition: none;
    }
}

.avatar-group .avatar img {
    border: 2px solid #FFF;
}

.avatar-group .avatar:hover {
    z-index: 2;
}

.avatar-group .avatar + .avatar {
    margin-left: -1.25rem;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.avatar-group .avatar-sm + .avatar-sm {
    margin-left: -1rem;
}

.avatar-group:hover .avatar {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
}

.avatar-group:hover .avatar-sm {
    border-top-left-radius: 0.2rem;
    border-bottom-left-radius: 0.2rem;
}

.hover-avatar-ungroup:hover .avatar:not(:first-child) {
    margin-left: 0;
}

.avatar-parent-child {
    display: inline-block;
    position: relative;
}

.avatar-child {
    position: absolute;
    right: 0;
    bottom: 0;
    background-color: #fff;
    border: 2px solid #FFF;
    border-radius: 0.2rem;
}

.avatar.rounded-circle + .avatar-child {
    border-radius: 50%;
}

.avatar + .avatar-child {
    width: 20px;
    height: 20px;
}

.avatar-lg + .avatar-child {
    width: 24px;
    height: 24px;
}

.avatar-sm + .avatar-child {
    width: 16px;
    height: 16px;
}

.avatar + .avatar-badge {
    width: 14px;
    height: 14px;
    right: -6px;
    bottom: 15px;
}

.avatar-lg + .avatar-badge {
    width: 16px;
    height: 16px;
    right: -5px;
    bottom: 20px;
}

.avatar-sm + .badge {
    width: 12px;
    height: 12px;
    right: -6px;
    bottom: 10px;
}

.avatar-connect {
    position: relative;
}

.avatar-connect:before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    border-bottom: 2px dashed #EFF2F7;
}

.avatar-connect ul {
    margin: 0;
    padding: 0;
    list-style: none;
}





.rating {
    border: none;
    float: left;
}

.rating>input {
    display: none;
}

.rating>label:before {
    margin: 5px;
    font-size: 1.25em;
    font-family: FontAwesome;
    display: inline-block;
    content: "\f005";
}

.rating>.half:before {
    content: "\f089";
    position: absolute;
}

.rating>label {
    color: #ddd;
    float: right;
}

.rating>input:checked~label,
.rating:not(:checked)>label:hover,
.rating:not(:checked)>label:hover~label {
    color: #FFD700;
}

.rating>input:checked+label:hover,
.rating>input:checked~label:hover,
.rating>label:hover~input:checked~label,
.rating>input:checked~label:hover~label {
    color: #FFED85;
}
.table td .progress {
    height: 7px;
    width: 120px;
    margin: 0;
}

.mtt{
    margin-top: 35px;
}


.custom_messanger_counter {
    position: relative;
    top: -15px;
    left: -5px;
}

.dash-sidebar .main-logo {
    justify-content: center;
    /*height: 100%;*/
    min-height: 80px;
    max-height: 80px;
    width: 100%;
    min-width: 255px;
    /*max-width: 255px;*/
}
a.b-brand {
    height: 100%;
    width: 100%;
}
.dash-sidebar .main-logo a img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    width: auto !important;
    height: auto;
    max-width: -webkit-fill-available !important;
    max-height: -webkit-fill-available !important;
    max-width: -moz-available;
    max-height: -moz-available;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    vertical-align: middle;
}
.m-view-btn {
    width: 105px;
    padding: 8px 10px;
    line-height: normal;
    border-radius: 10px;
    color: #fff;
}
.m-view-btn a {
    float: right;
    color: #fff;
    text-decoration: underline;
}

.white-sapce-nowrap {
    white-space: nowrap;
}
.list-group-flush > .list-group-item:last-child {
    border: none;
    border-bottom-width: 0;
}

.active_color{
    border: 2px solid #000 !important;
}
.display-total-time {
    font-size: 14px;
    font-weight: 500;
    height: 50px;
    border: 1px solid #008ECC;
    padding: 12px;
    background: #008ECC;
    text-align: center;
    border-radius: 4px;
    width: 100%;
    color:black;
}

.swiper-container {
    margin: 0 auto;
    position: relative;
    overflow: hidden;
    list-style: none;
    padding: 0;
    z-index: 1;
}

.rating-stars ul {
    list-style-type: none;
    padding: 0;
    -moz-user-select: none;
    -webkit-user-select: none;
}

.rating-stars ul > li.star {
    display: inline-block;
}
.rating-stars ul > li.star.selected > i.ti {
    color: #FF912C;

}
.rating-stars ul > li.star.selected > i.fa {
    fill: #FF912C;
}


.navbar.default.top-nav-collapse {
    background: transparent;
    box-shadow: none;
}
.active_color{
    border: 2px solid #000 !important;
}
.login-deafult{
    width: 139px !important;
}
/* card-icon-text-space */
.card-icon-text-space{
    margin-right: 5px;
}


.horizontal-scroll-cards p {
    width: 120px;
}
.horizontal-scroll-cards  img {
    border: 2px solid #51459d;
    padding: 3px;
}
.full-circle {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.card-body.project_table {
    height: 400px;
    overflow-y: auto;
}
.project_table::-webkit-scrollbar {
    width: 3px;
}
.project_table::-webkit-scrollbar-track {
    box-shadow: inset 0 0 3px #f2f2f2;
}

.project_table::-webkit-scrollbar-thumb {
    background-color: #bababa;
}

.round-img {
    width: 80px;
    height: 80px;
    object: cover !important;
}


.big-logo {
    width: 150px;
    height: 60px;
}
.card-2{
    height: 100%;
    max-height: 480px;
    margin-bottom: 0;
}
.full-card {
    min-height: 236px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
.fulls-card{
    min-height: 180px;
}

.kanban-card img {
    position: relative;
    width: 39px;
    height: 38px;
    border-radius: 50% !important;
    z-index: 2;
    transition: all 0.1s ease-in-out;
    border: 2px solid #ffffff;
}


.grid_user_image img {
    margin-left: -10px;
    border: 2px solid #dbdbdb;
}

.dataTable-table > thead > tr > th {
    padding: 15px 40px 15px 0;
}
.dataTable-sorter::before, .dataTable-sorter::after {
    right: -20px;
}
.status_badge {
    min-width: 87px;
}


.plan_card {
    width: 25%;
    float: left;
    margin-bottom: 20px;
}
.plan_card .card-body {
    min-height: 450px;
}
.active-tag{
    position: absolute;
    right: 20px;
}
.display-total-time{
    margin-top: 0;
}
@media only screen and (max-width: 1700px) {
    .plan_card {
        width: 33.33%;
        float: left;
    }
}

@media only screen and (max-width: 1440px) {
    .plan_card {
        width: 33.33%;
        float: left;
    }
}

@media only screen and (max-width: 1366px) {
    .plan_card {
        width: 50%;
        float: left;
    }
}

@media only screen and (max-width: 1199px) {
    .plan_card {
        width: 50%;
        float: left;
    }
}



@media only screen and (max-width: 991px) {
    .plan_card {
        width: 100%;
        float: left;
    }
}

.doc_status_badge {
    min-width: 100px;
}

.list_card{
    min-height: 400px;
}
.customer_card{
    height: 100%;
}
.vendor_card{
    height: 180px;
}
.logo_card{
    min-height: 280px;
}
.img_preview{
    width: 150px;
    height: 130px;
}
.setting_logo{
    top: -35px;
}
.choose-files input[type="file"]{
    display: none ;
}
.drp-languages .drp-language .dropdown-toggle{
    color: #525B69;
    display: inline-flex;
    align-items: center;
    padding: 0.6rem 0.7rem;
    margin: 0 7.5px;
    border-radius: 4px;
    position: relative;
    font-weight: 500;
    border-radius: 12px;
    border: 1px solid rgba(206, 206, 206, 0.2);
}
.email_temp{
    height: 450px !important;
    overflow-y: scroll;
}
.emp_details{
    min-height: 420px !important;
}
.green-text{
    color: green;
}
.red-text{
    color: red;
}
.activity-scroll{
    overflow: scroll;
    height: 500px;
}
.leads-scroll{
    overflow-y: scroll;
    max-height: 400px;
}

.job-create{
    min-height: 488px;
}
.email-color{
    background: #FFFFFF;
}

.svg-inline--fa.fa-w-16 {
    width: 1em;
}
.svg-inline--fa.fa-w-20 {
    width: 1.25em;
}

/*POS SYSTEM CSS*/
.purchase_status{
    min-width: 95px;
}

.pos-top-bar{
    background: #6fd944;
    border-radius: 10px;
    padding: 15px;
}

.product-list-block .product-custom-card {
    border-radius: 10px;
    box-shadow: 0 4px 20px 1px rgba(0,0,0,.06),0 1px 4px rgba(0,0,0,.08);
    cursor: pointer;
    margin-bottom: 16px;
    margin-left: 8px;
    margin-right: 8px;
    outline: 1px solid #e0e3ff;
    width: calc(24.96% - 16px);
}

.product-list-block .product-custom-card .card {
border-color: transparent!important;
border-radius: 10px;
border: 0;
box-shadow: none;
    margin-bottom: 0;
}
.product-list-block .product-custom-card .card .card-img-top {
height: 100px!important;
max-height: 100px!important;
-o-object-fit: contain;
object-fit: contain;
width: 100%!important;
border:0;
}
.product-list-block .product-custom-card h6{
    font-size: 14px;
}

.product-list-block .product-custom-card .fs-small {
    font-size: 12px;
}

 .product-list-block .product-custom-card .item-badges {
    position: absolute;
    left: 0;
    top: -3px;
}

.product-list-block .product-custom-card__card-badge {
    border-radius: 4px;
    font-size: 11px;
    font-weight: 400;
    line-height: 1;
    padding: .25em .4em;
    text-align: center;
    vertical-align: baseline;
    white-space: nowrap;
}


.right-content .product-list-block {
    height: auto;
    max-height: 500px;
    overflow-y: auto;
    display: flex;
    flex-wrap: wrap;
    padding-top: 25px;
}

.right-content .button-list__item-active {
    background-color: #6571ff!important;
    border-color: #6571ff!important;
    color: #FFFFFF;
}
.sub-total .total-price{
    padding-bottom: 15px;
    border-bottom: 2px dashed #c5c5c5;
}
.button-list .button-list__item{
    margin-bottom: 0.5rem;
}
.cat-pad{
    background-color: #6fd944;
}

@media screen and (max-width:767px){

    .product-list-block .product-custom-card{
        width: 100%;
    }
}


.carttable .quantity.buttons_added {
    text-align: left;
    position: relative;
    white-space: nowrap;
    vertical-align: top;
}

.carttable .quantity.buttons_added .minus {
    border-right: 0;
}

.carttable .quantity.buttons_added input {
    display: inline-block;
    margin: 0;
    vertical-align: top;
    box-shadow: none;
}

.carttable .quantity .input-number {
    width: 60px;
    height: 35px;
    padding: 0 0px;
    text-align: center;
    background-color: transparent;
    border: 1px solid #efefef;
}

.carttable .quantity.buttons_added .plus {
    border-left: 0;
}

.carttable .quantity.buttons_added .minus,
.carttable .quantity.buttons_added .plus {
    padding: 4px 10px 8px;
    height: 35px;
    background-color: #ffffff;
    border: 1px solid #efefef;
    cursor: pointer;
}

.form-row>.zoom-in {
    transition-property: background-color, border-color, color, fill, opacity, box-shadow, transform;
    transition-duration: .3s;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --transform-translate-x: 0;
    --transform-translate-y: 0;
    --transform-rotate: 0;
    --transform-skew-x: 0;
    --transform-skew-y: 0;
    --transform-scale-x: 1;
    --transform-scale-y: 1;
    transform: translateX(var(--transform-translate-x)) translateY(var(--transform-translate-y)) rotate(var(--transform-rotate)) skewX(var(--transform-skew-x)) skewY(var(--transform-skew-y)) scaleX(var(--transform-scale-x)) scaleY(var(--transform-scale-y));
    cursor: pointer;
    margin-bottom: 10px !important;
}

.zoom-in:hover {
    --transform-scale-x: 1.05;
    --transform-scale-y: 1.05;
}
#product-listing .toacart {
    cursor: pointer;
}
.cat-list-btn .btn-primary {
    color: #3f3f3f !important;
}

body.theme-1 .cat-list-btn .btn-primary {
    background: #fff !important;
    border-color: #fff !important;
}
body.theme-1 .cat-list-btn .cat-active .btn-primary {
    background: #0CAF60 !important;
    border-color: #0CAF60 !important;
    color: #fff !important;
}

body.theme-2 .cat-list-btn .btn-primary {
    background-color: #fff !important;
    border-color: #fff !important;
}
body.theme-2 .cat-list-btn .cat-active .btn-primary {
    background-color: #584ED2 !important;
    border-color: #584ED2 !important;
    color: #fff !important;
}

body.theme-3 .cat-list-btn .btn-primary {
    background-color: #fff !important;
    border-color: #fff !important;
}
body.theme-3 .cat-list-btn .cat-active .btn-primary {
    background-color: #6fd943 !important;
    border-color: #6fd943 !important;
    color: #fff !important;
}

body.theme-4 .cat-list-btn .btn-primary {
    background-color: #fff !important;
    border-color: #fff !important;
}
body.theme-4 .cat-list-btn .cat-active .btn-primary {
    background-color: #145388 !important;
    border-color: #145388 !important;
    color: #fff !important;
}
body.theme-5 .cat-list-btn .btn-primary {
    background-color: #fff !important;
    border-color: #fff !important;
}
body.theme-5 .cat-list-btn .cat-active .btn-primary {
    background-color: #B9406B !important;
    border-color: #B9406B !important;
    color: #fff !important;
}
body.theme-6 .cat-list-btn .btn-primary {
    background-color: #fff !important;
    border-color: #fff !important;
}
body.theme-6 .cat-list-btn .cat-active .btn-primary {
    background-color: #008ECC !important;
    border-color: #008ECC !important;
    color: #fff !important;
}
body.theme-7 .cat-list-btn .btn-primary {
    background-color: #fff !important;
    border-color: #fff !important;
}
body.theme-7 .cat-list-btn .cat-active .btn-primary {
    background-color: #922C88 !important;
    border-color: #922C88 !important;
    color: #fff !important;
}
body.theme-8 .cat-list-btn .btn-primary {
    background-color: #fff !important;
    border-color: #fff !important;
}
body.theme-8 .cat-list-btn .cat-active .btn-primary {
    background-color: #C0A145 !important;
    border-color: #C0A145 !important;
    color: #fff !important;
}
body.theme-9 .cat-list-btn .btn-primary {
    background-color: #fff !important;
    border-color: #fff !important;
}
body.theme-9 .cat-list-btn .cat-active .btn-primary {
    background-color: #48494B !important;
    border-color: #48494B !important;
    color: #fff !important;
}
body.theme-10 .cat-list-btn .btn-primary {
    background-color: #fff !important;
    border-color: #fff !important;
}
body.theme-10 .cat-list-btn .cat-active .btn-primary {
    background-color: #0C7785 !important;
    border-color: #0C7785 !important;
    color: #fff !important;
}


.top-badge {
    position: absolute;
    top: 0;
    right: 0;
}
.product-title-name {
    font-size: 13px;
    font-weight: 600;
    flex: 1;
    min-height: 33px;
}
.product-body-nop .form-row [class*="col-"] {
    display: flex;
}
.cat-active .btn {
    color: #fff;
}
.tab-btns {
    min-width: 100px;
    white-space: nowrap;
    border-radius: 0.625rem!important;
    padding: 10px 20px;
    font-size: 12px;
}
.cart-product-list .table tr th {
    padding: 5px 15px !important;
}
.total-price h6 {
    font-size: 11px;
}
.product-body-nop .card:not(:last-of-type) {
    margin-bottom: 12px;
}
.product-body-nop {
    height: calc(100vh - 260px);
    overflow-y: auto;
    overflow-x: hidden;
    margin-right: -7px;
    padding-right: 7px;
}
.product-body-nop::-webkit-scrollbar {
    width: 5px;
    margin-right: -5px;
}
.product-body-nop::-webkit-scrollbar-thumb {
    background-color: lightgrey;
    border-radius: 10px;
}
.sop-card {
    margin-bottom: 10px;
}

.carttable-scroll {
    height: calc(100vh - 115px);
}
.carttable-scroll .table-responsive {
    height: calc(100vh - 300px);
}
.carttable-scroll .table-responsive::-webkit-scrollbar {
    width: 5px;
    height: 5px;
    margin-right: -5px;
}
.carttable-scroll .table-responsive::-webkit-scrollbar-thumb {
    background-color: lightgrey;
    border-radius: 10px;
}
.carttable-scroll .name,
.carttable-scroll .tax {
    padding: 0 !important;
}
.carttable-scroll .tax {
    text-align: center;
}
.total-section {
    width: 100%;
    background: #f1f1f1;
    padding: 15px 15px;
    border-radius: 10px;
    box-shadow: 0px 10px 10px -10px #97979780;
}

.sop-card {
    height: 100%;
}

.product-body-nop .card{
    width:100%;
}
.product-body-nop .card  .avatar {
    object-fit: scale-down;
    padding: 20px 0 10px;
    background: transparent;
    min-height: 9rem;
    width: 100%;
}
.product-body-nop .card  .card-body {
    flex: 1 1 auto;
    padding: 25px 25px;
    display: flex;
    flex-direction: column;
}
.product-body-nop .card  .badge {
    padding: 4px 10px;
    width: auto;
    max-width: 100px;
    margin: 0 auto 0 0;
}
.product-body-nop .card  .shadow{
    box-shadow: none !important;
}
.toacart{
    display: flex;
}
.product-body-nop .card .product-title-name{
    min-height: unset;
}

/*END POS SYSTEM CSS*/

/*for messages counter*/
.message-counter {
    position: absolute !important;
    top: fpx !important;
    right: 4px !important;
    border-radius: 50%;
    font-size: 10px;
    left: 22px !important;
    width: 15px !important;
    height: 15px;
    text-align: center !important;
    color: #fff !important;
}

/*for messages counter*/

.status-drp .dash-head-link {
    color: #525b69;
    display: inline-flex;
    align-items: center;
    padding: 0.6rem 0.7rem;
    margin: 0 4px;
    border-radius: 12px;
    position: relative;
    font-weight: 500;
    border: 1px solid rgba(206, 206, 206, 0.2);
    background-color: #fff;
}

/* start Rating Star Widgets Style - job application star*/
.rating-stars ul {
    list-style-type: none;
    padding: 0;

    -moz-user-select: none;
    -webkit-user-select: none;
}

.rating-stars ul > li.star {
    display: inline-block;

}
.rating-stars ul > li.star > i.fas {
    font-size: 1.5em; /* Change the size of the stars */
    color: #ccc; /* Color on idle state */
}

/* Hover state of the stars */
.rating-stars ul > li.star.hover > i.fas {
    color: #FFCC36;
}

/* Selected state of the stars */
.rating-stars ul > li.star.selected > i.fas {
    color: #ffa21d;
}


.static-rating {
    display: inline-block;
}

.static-rating .star {
    color: #E0E6ED;
}

.static-rating .voted {
    color: #ffa21d;
}

/* end start Rating Star Widgets Style - job application star*/



/* project_report */
.img_group {
    margin-left: -14px;
}
.circular-progressbar .flex-wrapper {
    display: flex;
    flex-flow: row nowrap;
}

.circular-progressbar .single-chart {
    width: 60%;
    justify-content: space-around;
}

.circular-progressbar .circular-chart {
    display: block;
    margin: 10px auto;
    max-width: 100%;
    max-height: 550px;
}

.circular-progressbar .circle-bg {
    fill: none;
    stroke: #eee;
    stroke-width: 3.8;
}

.circular-progressbar .circle {
    fill: none;
    stroke-width: 2.8;
    stroke-linecap: round;
    animation: progress 1s ease-out forwards;
}

@keyframes progress {
    0% {
        stroke-dasharray: 0 100;
    }
}

.circular-progressbar .circular-chart.red .circle {
    stroke: #ff3a6e;
}

.circular-progressbar .circular-chart.orange .circle {
    stroke: #fd7e14;
}

.circular-progressbar .circular-chart.green .circle {
    stroke: #6fd943;
}

.circular-progressbar .circular-chart.blue .circle {
    stroke: #3c9ee5;
}

.circular-progressbar .percentage {
    /* fill: #666;*/
    font-family: sans-serif;
    font-size: 0.5em;
    text-anchor: middle;
}
.circular-progressbar .flex-wrapper {
    display: flex;
    flex-flow: row nowrap;
}

.circular-progressbar .single-chart {
    width: 60%;
    justify-content: space-around;
}

.circular-progressbar .circular-chart {
    display: block;
    margin: 10px auto;
    max-width: 100%;
    max-height: 550px;
}

.circular-progressbar .circle-bg {
    fill: none;
    stroke: #eee;
    stroke-width: 3.8;
}

.circular-progressbar .circle {
    fill: none;
    stroke-width: 2.8;
    stroke-linecap: round;
    animation: progress 1s ease-out forwards;
}

@keyframes progress {
    0% {
        stroke-dasharray: 0 100;
    }
}

.circular-progressbar .circular-chart.red .circle {
    stroke: #ff3a6e;
}

.circular-progressbar .circular-chart.orange .circle {
    stroke: #fd7e14;
}

.circular-progressbar .circular-chart.green .circle {
    stroke: #6fd943;
}

.circular-progressbar .circular-chart.blue .circle {
    stroke: #3c9ee5;
}

.circular-progressbar .percentage {
    /* fill: #666;*/
    font-family: sans-serif;
    font-size: 0.5em;
    text-anchor: middle;
}
.pos-header{
    background-color: #e7e9ec;
}

.table.modal-table td,
.table.modal-table th {
    white-space: normal;
}
.task-calendar-scroll{
    overflow-y: scroll;
    height: 827px;
}


/*start pos thermal print */
.pos-module-tbl td, .pos-module-tbl th {
    padding: 0px !important;
}
.pos-module .product-border {
    border-bottom: 3px dotted #d8d8d8!important;
}
/*end pos print */


/*start balance-sheet new theme*/
.nav-pills.cust-nav {
    background: #E1E9ED;
}
.nav-pills.cust-nav .nav-item .nav-link {
    color: #162C4E;
}

.data-wrapper{
    /* height: 100%; */
    display: flex;
    flex-direction: column;
}
.data-wrapper .data-body{
    flex: 1;
}

/*.data-wrapper .data-body .list-group-item:nth-child(2) {*/
/*    flex: 1;*/
/*}*/

/* end balance-sheet new theme*/



/* JOB PAGE START */
.job-wrapper .navbar {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
}

.job-banner {
    position: relative;
    background-color: unset;
}

.job-banner .job-banner-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.job-banner .job-banner-bg img {
    width: 100%;
    height: 100%;
}

.job-banner .job-banner-content {
    padding: 5.9% 0;
    max-width: 360px;
    width: 100%;
    margin: auto;
}

.placedjob-section .section-title {
    padding: 35px 25px;
    border-radius: 15px;
    -webkit-border-radius: 15px;
    -moz-border-radius: 15px;
    -ms-border-radius: 15px;
    -o-border-radius: 15px;
    margin-top: -20px;
    text-align: center;
}

.job-card-body {
    padding: 15px;
    border-radius: 15px;
    background: linear-gradient(277.29deg, rgba(111, 217, 67, 0.09) 0.99%, rgba(111, 217, 67, 0) 100.89%);
    -webkit-border-radius: 15px;
    -moz-border-radius: 15px;
    -ms-border-radius: 15px;
    -o-border-radius: 15px;
    border: 1px solid #6FD943;
}

.placedjob-section {
    padding-bottom: 80px;
    background-color: unset;

}

.job-content .container {
    max-width: 1540px;
}

@media screen and (max-width:767px) {
    .job-banner .job-banner-content {
        padding: 10% 0;
    }
}

@media screen and (max-width:575px) {
    .job-banner .job-banner-content {
        padding: 21% 0;
    }
    .account-main-inner p, .account-inner{
        padding-left: 0 !important;
    }
}

.job-card{
    display: flex;
    flex-wrap: wrap;
}
.job-card .job-card-body{
    width: 100%;
    display: flex;
    flex-direction: column;
}
.job-card .job-card-body h5{
    flex: 1;
}

/* JOB PAGE END */


/* apply job section */

.apply-job-section {
    padding-bottom: 80px;
    background-color: unset;

}

.apply-job-section .apply-job-wrapper{
    padding: 35px 25px;
    border-radius: 15px;
    -webkit-border-radius: 15px;
    -moz-border-radius: 15px;
    -ms-border-radius: 15px;
    -o-border-radius: 15px;
    margin-top: -20px;
}

@media screen and (max-width:767px) {
    .job-banner .job-banner-content {
        padding: 10% 0;
    }
    .apply-job-section .apply-job-wrapper{
        padding: 35px 15px;
    }
}

@media screen and (max-width:575px) {
    .job-banner .job-banner-content {
        padding: 26% 0;
    }
}

/* apply job section */

.stage li{
    cursor: pointer;
}
@media (min-width: 420px) {
    .seo_image{
        height: 200px;
        width: 360px
    }
    }

    @media (max-width: 420px) {
        .seo_image{
            height: 150px;
            width: 200px
        }
        }
.disabledCookie {
    pointer-events: none;
    opacity: 0.4;
}
.cookie_btn{
    margin-right: 31px;
}


/*start for payment setting*/
.setting-accordion .accordion-item {
    border: 1px solid #E0E6EF !important;
    border-radius: 7px;
}

.setting-accordion .accordion-header {
    background: #F8F8F8;
    border-radius: 7px;
    -webkit-border-radius: 7px;
    -moz-border-radius: 7px;
    -ms-border-radius: 7px;
    -o-border-radius: 7px;
}

.setting-accordion .accordion-header .accordion-button {
    background: #F8F8F8 !important;
    display: flex;
    justify-content: space-between;
    border-radius: 7px;
    box-shadow: none;
    border-bottom: 1px solid transparent;
}

.setting-accordion .accordion-header .accordion-button:not(.collapsed) {
    border-color: #E0E6EF;
}

.setting-accordion .accordion-header .accordion-button span {
    flex: 1;
}

.setting-accordion .accordion-header .accordion-button::after {
    margin: 0 0 0 5px;
    width: 24px;
    height: 24px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='25' viewBox='0 0 24 25' fill='none'%3E%3Cpath opacity='0.4' d='M12 22.4146C17.5228 22.4146 22 17.9374 22 12.4146C22 6.8917 17.5228 2.41455 12 2.41455C6.47715 2.41455 2 6.8917 2 12.4146C2 17.9374 6.47715 22.4146 12 22.4146Z' fill='%2325314C'/%3E%3Cpath d='M15.5301 12.8845C15.2371 12.5915 14.762 12.5915 14.469 12.8845L12.749 14.6045V8.41455C12.749 8.00055 12.413 7.66455 11.999 7.66455C11.585 7.66455 11.249 8.00055 11.249 8.41455L11.249 14.6035L9.52908 12.8835C9.23608 12.5905 8.76104 12.5905 8.46804 12.8835C8.17504 13.1765 8.17504 13.6516 8.46804 13.9446L11.468 16.9446C11.537 17.0136 11.62 17.0684 11.711 17.1064C11.802 17.1444 11.9001 17.1646 11.9981 17.1646C12.0961 17.1646 12.1929 17.1444 12.2849 17.1064C12.3769 17.0684 12.4591 17.0136 12.5281 16.9446L15.5281 13.9446C15.8231 13.6516 15.8231 13.1775 15.5301 12.8845Z' fill='%2325314C'/%3E%3C/svg%3E");
    background-size: 24px;
    border-radius: 100%;
    -webkit-border-radius: 100%;
    -moz-border-radius: 100%;
    -ms-border-radius: 100%;
    -o-border-radius: 100%;
}

.setting-accordion .accordion-item:not(:last-of-type) {
    margin-bottom: 15px;
}
/*end for payment setting*/


.float-end a > i{
    color: #FFFFFF;
}
.modal-footer .btn-light{
    margin-right: 13px;
}


/*.fc-timegrid-event-harness-inset{*/
/*    inset: 494px 0% -566px !important;*/
/*}*/


.fc-timegrid-event-harness{
    position: absolute !important;
}
.status-btn{
    border-radius: 8px;
    color: #FFFFFF;
}

/* .ps--active-y  {
    height: 100vh !important;
} */


/*start - date:19-jun-2023*/
body.no-scroll{
    overflow: hidden;
    position: relative;
}

.auth-wrapper .navbar .navbar-brand{
    display: block;
    width: 100%;
    max-width: 150px;
}
.auth-wrapper .navbar .navbar-brand img{
    width: 100%;
}
@media (max-width: 1024px) {
    .ps {
        height: 100vh !important;
    }
}

.ps {
    overflow: hidden !important;
    overflow-anchor: none;
    touch-action: auto;
}

.dash-sidebar .navbar-content{
    height: calc(100vh - 70px);
}
/*end- date:19-jun-2023*/


.language_option_bg option {
    background-color: #fff;
    color: #000;
}
/*[data-action] {*/
/*    background: gray !important;*/
/*}*/



/*start for balancesheet*/
.account-first span:first-child{
    width: 35%;
}
.data-wrapper .data-body .list-group-item span:nth-child(1){
    width: 35%;
}
/*end for balancesheet*/


/*start for input search*/
.searchBoxElement{
    background-color: white;
    border: 1px solid #aaa;
    position: absolute;
    max-height: 150px;
    overflow-x: hidden;
    overflow-y: auto;
    margin: 0;
    padding: 0;
    line-height: 23px;
    list-style: none;
    z-index: 1;
    -ms-overflow-style: none;
    scrollbar-width: none;
}


.searchBoxElement span{
    padding: 0 5px;
}


.searchBoxElement li{
    background-color: white;
    color: black;
}

.searchBoxElement li:hover{
    background-color: #50a0ff;
    color: white;
}

.searchBoxElement li.selected{
    background-color: #50a0ff;
    color: white;
}

.formTextbox {
    display: block;
    width: 100%;
    padding: 0.575rem 1rem;
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 1.5;
    color: #293240;
    background-color: #ffffff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border-radius: 6px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}


body.theme-1 .formTextbox:focus{
    border-color: var(--theme1-color);
    box-shadow: 0 0 0 0.2rem rgb(12,175,96);
}
body.theme-2 .formTextbox:focus{
    border-color: var(--theme2-color);
    box-shadow: 0 0 0 0.2rem rgb(117,194,81);
}
body.theme-3 .formTextbox:focus{
    border-color: var(--theme3-color);
    box-shadow: 0 0 0 0.2rem rgb(88,78,210, 25%);
}
body.theme-4 .formTextbox:focus{
    border-color: var(--theme4-color);
    box-shadow: 0 0 0 0.2rem rgb(20,83,136, 25%);
}

body.theme-5 .formTextbox:focus{
    border-color: var(--theme5-color);
    box-shadow: 0 0 0 0.2rem rgb(185,64,107, 25%);
}

body.theme-6 .formTextbox:focus{
    border-color: var(--theme6-color);
    box-shadow: 0 0 0 0.2rem rgb(0,142,204, 25%);
}
body.theme-7 .formTextbox:focus{
    border-color: var(--theme7-color);
    box-shadow: 0 0 0 0.2rem rgb(146,44,136, 25%);
}
body.theme-8 .formTextbox:focus{
    border-color: var(--theme8-color);
    box-shadow: 0 0 0 0.2rem rgb(192,161,69, 25%);
}
body.theme-9 .formTextbox:focus{
    border-color: var(--theme9-color);
    box-shadow: 0 0 0 0.2rem rgb(72,73,75, 25%);
}
body.theme-10 .formTextbox:focus{
    border-color: var(--theme10-color);
    box-shadow: 0 0 0 0.2rem rgb(12,119,133, 25%);
}
/*end for input search*/
.account-inner p{
    max-width: 25%;
    width: 100%;
}

.aacount-title h6{
    max-width: 25%;
    width: 100%;
}

.list_colume_notifi {
    position: relative;
    display: block;
    padding: 16.66667px 25px;
    color: #212529;
    /* background-color: #ffffff; */
    border: 1px solid #f1f1f1;
}
@media (max-width: 420px) {
.action-btn-col{
    flex: 0 0 100%;
    text-align: left;
    justify-content: left;
    display: flex;
    margin-top: 10px;
}
}

.account-arrow{
    display: flex;
    align-items: center;
    gap: 5px;
    flex: 0 0 25%;

}
.account-arrow p{
    flex: 0 0 25%;
}
.account-arrow .account-icon{
    width: 15px;
    height: 15px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    margin-left: -20px;
    font-size: 12px;
}
body.theme-1 .account-arrow .account-icon{
    background-color: #0CAF60;
}
body.theme-2 .account-arrow .account-icon{
    background-color: #584ED2;        
}
body.theme-3 .account-arrow .account-icon{
    background-color: #6FD943;
}
body.theme-4 .account-arrow .account-icon{
    background-color: #145388;
}
body.theme-5 .account-arrow .account-icon{
    background-color: #B9406B;
}
body.theme-6 .account-arrow .account-icon{
    background-color: #008ECC;
}
body.theme-7 .account-arrow .account-icon{
    background-color: #922C88;
}
body.theme-8 .account-arrow .account-icon{
    background-color: #C0A145;
}
body.theme-9 .account-arrow .account-icon{
    background-color: #48494B;
}
body.theme-10 .account-arrow .account-icon{
    background-color: #0C7785;
}

.account-arrow .account-icon i{
    transition: .5s all ease-in-out;
}
.collapse-view .account-arrow i{
    transform: rotate(180deg);
}

.subAccount {
    font-weight: 600;
  }

  .apexcharts-legend-text {
    margin-right: 10px;
}

[dir="rtl"] .apexcharts-xaxis-texts-g {
    transform: translate(10px, 40px) !important;
}

/* .apexcharts-yaxis-title-text
{
    transform: rotate(-90deg);
    transform-origin: 0px 133.34800720214844px !important;
}
.apexcharts-yaxis
{
    transform: translate(0px, 0px) !important;
}
.apexcharts-pie-area
{
    display: none;
} */

.color-wrp .color-picker-wrp input[type="color"] {
    background-color: #fff;
    height: 55px;
    cursor: pointer;
    border-radius: 3px;
    margin: 0px;
    padding: 0px;
    border: 0;
    margin-bottom: 5px;
    margin-left: 5px;
}

.color-wrp{
    display: flex;
    align-items: center;
    margin-top: 15px;
}
.color-wrp .theme-color{
    margin: 0;
}
.color-wrp .color-picker-wrp{
    width: 100px;
}

#calender_type
{
    float: right;
    width : 150px;
}

.dash-header .drp-notification {
    margin-left: 10px;
}

.budget .btn{
    margin-top: 10px;
    margin-right: 10px;
}

.theme-avtar-logo
{
width: 80px;
border-radius: 17.3552px;
color: #fff;
display: flex;
align-items: center;
justify-content: center;
font-size: 20px;
}
/* google recapch  */

.grecaptcha-badge{
    z-index: 2;
}

.border-grey {
    border: 1px solid #cbcbcb !important;
}
.upgrade-line hr {
    flex: 1;
}

input[type="time"] {
    font-size: smaller; 
}
.note-statusbar {
    width: 98%;
    margin-left: 6px;
}
@media only screen and (min-width: 1025px) and (max-width: 1210px) {
    .fc .fc-toolbar {
        flex-direction: column;
    }

}
