document.addEventListener("DOMContentLoaded", function () {
    var rightcard = false;
    var tempblock;
    var tempblock2;
    const grabmeIconUrl = "{{ asset('public/workflow/assets/grabme.svg') }}";
    const eyeIconUrl = "{{ asset('public/workflow/assets/eye.svg') }}";
    const databaseIconurl = "{{asset('public/workflow/assets/database.svg')}}";
    const iconactionurl = "{{asset('public/workflow/assets/action.svg')}}";
    const timeiconurl = "{{asset('public/workflow/assets/time.svg')}}";
    const errorIconUrl = "{{asset('public/workflow/assets/error.svg')}}";
    const twitterIconUrl = "{{asset('public/workflow/assets/twitter.svg')}}";
    const logIconUrl = "{{asset('public/workflow/assets/log.svg')}}";
    document.getElementById("blocklist").innerHTML = `
        <div class="blockelem create-flowy noselect">
            <input type="hidden" name="blockelemtype" class="blockelemtype" value="1">
            <div class="grabme">
                <img src=${grabmeIconUrl}>
            </div>
            <div class="blockin">
                <div class="blockico">
                    <span></span>
                    <img src=${eyeIconUrl}>
                </div>
                <div class="blocktext">
                    <p class="blocktitle">New visitor</p>
                    <p class="blockdesc">Triggers when somebody visits a specified page</p>
                </div>
            </div>
        </div>`;
    flowy(document.getElementById("canvas"), drag, release, snapping);
    function addEventListenerMulti(type, listener, capture, selector) {
        var nodes = document.querySelectorAll(selector);
        for (var i = 0; i < nodes.length; i++) {
            nodes[i].addEventListener(type, listener, capture);
        }
    }
    function snapping(drag, first) {
        var grab = drag.querySelector(".grabme");
        grab.parentNode.removeChild(grab);
        var blockin = drag.querySelector(".blockin");
        blockin.parentNode.removeChild(blockin);

        const assets = {
            eyeblue: "{{ asset('public/workflow/assets/eyeblue.svg') }}",
            more: "{{ asset('public/workflow/assets/more.svg') }}",
            actionblue: "{{ asset('public/workflow/assets/actionblue.svg') }}",
            timeblue: "{{ asset('public/workflow/assets/timeblue.svg') }}",
            errorblue: "{{ asset('public/workflow/assets/errorblue.svg') }}",
            databaseorange: "{{ asset('public/workflow/assets/databaseorange.svg') }}",
            actionorange: "{{ asset('public/workflow/assets/actionorange.svg') }}",
            twitterorange: "{{ asset('public/workflow/assets/twitterorange.svg') }}",
            logred: "{{ asset('public/workflow/assets/logred.svg') }}",
            errorred: "{{ asset('public/workflow/assets/errorred.svg') }}"
        };

        if (drag.querySelector(".blockelemtype").value == "1") {
            drag.innerHTML += `
                <div class='blockyleft'>
                    <img src="${assets.eyeblue}"><p class='blockyname'>New visitor</p>
                </div>
                <div class='blockyright'><img src="${assets.more}"></div>
                <div class='blockydiv'></div>
                <div class='blockyinfo'>When a <span>new visitor</span> goes to <span>Site 1</span></div>`;
        } else if (drag.querySelector(".blockelemtype").value == "2") {
            drag.innerHTML += `
                <div class='blockyleft'>
                    <img src="${assets.actionblue}"><p class='blockyname'>Action is performed</p>
                </div>
                <div class='blockyright'><img src="${assets.more}"></div>
                <div class='blockydiv'></div>
                <div class='blockyinfo'>When <span>Action 1</span> is performed</div>`;
        } else if (drag.querySelector(".blockelemtype").value == "3") {
            drag.innerHTML += `
                <div class='blockyleft'>
                    <img src="${assets.timeblue}"><p class='blockyname'>Time has passed</p>
                </div>
                <div class='blockyright'><img src="${assets.more}"></div>
                <div class='blockydiv'></div>
                <div class='blockyinfo'>When <span>10 seconds</span> have passed</div>`;
        } else if (drag.querySelector(".blockelemtype").value == "4") {
            drag.innerHTML += `
                <div class='blockyleft'>
                    <img src="${assets.errorblue}"><p class='blockyname'>Error prompt</p>
                </div>
                <div class='blockyright'><img src="${assets.more}"></div>
                <div class='blockydiv'></div>
                <div class='blockyinfo'>When <span>Error 1</span> is triggered</div>`;
        } else if (drag.querySelector(".blockelemtype").value == "5") {
            drag.innerHTML += `
                <div class='blockyleft'>
                    <img src="${assets.databaseorange}"><p class='blockyname'>New database entry</p>
                </div>
                <div class='blockyright'><img src="${assets.more}"></div>
                <div class='blockydiv'></div>
                <div class='blockyinfo'>Add <span>Data object</span> to <span>Database 1</span></div>`;
        } else if (drag.querySelector(".blockelemtype").value == "6") {
            drag.innerHTML += `
                <div class='blockyleft'>
                    <img src="${assets.databaseorange}"><p class='blockyname'>Update database</p>
                </div>
                <div class='blockyright'><img src="${assets.more}"></div>
                <div class='blockydiv'></div>
                <div class='blockyinfo'>Update <span>Database 1</span></div>`;
        } else if (drag.querySelector(".blockelemtype").value == "7") {
            drag.innerHTML += `
                <div class='blockyleft'>
                    <img src="${assets.actionorange}"><p class='blockyname'>Perform an action</p>
                </div>
                <div class='blockyright'><img src="${assets.more}"></div>
                <div class='blockydiv'></div>
                <div class='blockyinfo'>Perform <span>Action 1</span></div>`;
        } else if (drag.querySelector(".blockelemtype").value == "8") {
            drag.innerHTML += `
                <div class='blockyleft'>
                    <img src="${assets.twitterorange}"><p class='blockyname'>Make a tweet</p>
                </div>
                <div class='blockyright'><img src="${assets.more}"></div>
                <div class='blockydiv'></div>
                <div class='blockyinfo'>Tweet <span>Query 1</span> with the account <span>@alyssaxuu</span></div>`;
        } else if (drag.querySelector(".blockelemtype").value == "9") {
            drag.innerHTML += `
                <div class='blockyleft'>
                    <img src="${assets.logred}"><p class='blockyname'>Add new log entry</p>
                </div>
                <div class='blockyright'><img src="${assets.more}"></div>
                <div class='blockydiv'></div>
                <div class='blockyinfo'>Add new <span>success</span> log entry</div>`;
        } else if (drag.querySelector(".blockelemtype").value == "10") {
            drag.innerHTML += `
                <div class='blockyleft'>
                    <img src="${assets.logred}"><p class='blockyname'>Update logs</p>
                </div>
                <div class='blockyright'><img src="${assets.more}"></div>
                <div class='blockydiv'></div>
                <div class='blockyinfo'>Edit <span>Log Entry 1</span></div>`;
        } else if (drag.querySelector(".blockelemtype").value == "11") {
            drag.innerHTML += `
                <div class='blockyleft'>
                    <img src="${assets.errorred}"><p class='blockyname'>Prompt an error</p>
                </div>
                <div class='blockyright'><img src="${assets.more}"></div>
                <div class='blockydiv'></div>
                <div class='blockyinfo'>Trigger <span>Error 1</span></div>`;
        }

        return true;
    }

    function drag(block) {
        block.classList.add("blockdisabled");
        tempblock2 = block;
    }
    function release() {
        if (tempblock2) {
            tempblock2.classList.remove("blockdisabled");
        }
    }
    var disabledClick = function () {
        document.querySelector(".navactive").classList.add("navdisabled");
        document.querySelector(".navactive").classList.remove("navactive");
        this.classList.add("navactive");
        this.classList.remove("navdisabled");
        if (this.getAttribute("id") == "triggers") {
            document.getElementById("blocklist").innerHTML = '<div class="blockelem create-flowy noselect"><input type="hidden" name="blockelemtype" class="blockelemtype" value="1"><div class="grabme"><img src="${grabmeIconUrl}"></div><div class="blockin">                  <div class="blockico"><span></span><img src="${eyeIconUrl}"></div><div class="blocktext">                        <p class="blocktitle">New visitor</p><p class="blockdesc">Triggers when somebody visits a specified page</p>        </div></div></div><div class="blockelem create-flowy noselect"><input type="hidden" name="blockelemtype" class="blockelemtype" value="2"><div class="grabme"><img src="${grabmeIconUrl}"></div><div class="blockin">                    <div class="blockico"><span></span><img src="${iconactionurl}"></div><div class="blocktext">                        <p class="blocktitle">Action is performed</p><p class="blockdesc">Triggers when somebody performs a specified action</p></div></div></div><div class="blockelem create-flowy noselect"><input type="hidden" name="blockelemtype" class="blockelemtype" value="3"><div class="grabme"><img src="${grabmeIconUrl}"></div><div class="blockin">                    <div class="blockico"><span></span><img src="${timeiconurl}"></div><div class="blocktext">                        <p class="blocktitle">Time has passed</p><p class="blockdesc">Triggers after a specified amount of time</p>          </div></div></div><div class="blockelem create-flowy noselect"><input type="hidden" name="blockelemtype" class="blockelemtype" value="4"><div class="grabme"><img src="${grabmeIconUrl}"></div><div class="blockin">                    <div class="blockico"><span></span><img src="${errorIconUrl}"></div><div class="blocktext">                        <p class="blocktitle">Error prompt</p><p class="blockdesc">Triggers when a specified error happens</p>              </div></div></div>';
        } else if (this.getAttribute("id") == "actions") {
            document.getElementById("blocklist").innerHTML = '<div class="blockelem create-flowy noselect"><input type="hidden" name="blockelemtype" class="blockelemtype" value="5"><div class="grabme"><img src="${grabmeIconUrl}"></div><div class="blockin">                  <div class="blockico"><span></span><img src="${databaseIconurl}"></div><div class="blocktext">                        <p class="blocktitle">New database entry</p><p class="blockdesc">Adds a new entry to a specified database</p>        </div></div></div><div class="blockelem create-flowy noselect"><input type="hidden" name="blockelemtype" class="blockelemtype" value="6"><div class="grabme"><img src="${grabmeIconUrl}"></div><div class="blockin">                  <div class="blockico"><span></span><img src="${databaseIconurl}"></div><div class="blocktext">                        <p class="blocktitle">Update database</p><p class="blockdesc">Edits and deletes database entries and properties</p>        </div></div></div><div class="blockelem create-flowy noselect"><input type="hidden" name="blockelemtype" class="blockelemtype" value="7"><div class="grabme"><img src="${grabmeIconUrl}"></div><div class="blockin">                  <div class="blockico"><span></span><img src="${iconactionurl}"></div><div class="blocktext">                        <p class="blocktitle">Perform an action</p><p class="blockdesc">Performs or edits a specified action</p>        </div></div></div><div class="blockelem create-flowy noselect"><input type="hidden" name="blockelemtype" class="blockelemtype" value="8"><div class="grabme"><img src="${grabmeIconUrl}"></div><div class="blockin">                  <div class="blockico"><span></span><img src="${twitterIconUrl}"></div><div class="blocktext">                        <p class="blocktitle">Make a tweet</p><p class="blockdesc">Makes a tweet with a specified query</p>        </div></div></div>';
        } else if (this.getAttribute("id") == "loggers") {
            document.getElementById("blocklist").innerHTML = '<div class="blockelem create-flowy noselect"><input type="hidden" name="blockelemtype" class="blockelemtype" value="9"><div class="grabme"><img src="${grabmeIconUrl}"></div><div class="blockin">                  <div class="blockico"><span></span><img src="${logIconUrl}"></div><div class="blocktext">                        <p class="blocktitle">Add new log entry</p><p class="blockdesc">Adds a new log entry to this project</p>        </div></div></div><div class="blockelem create-flowy noselect"><input type="hidden" name="blockelemtype" class="blockelemtype" value="10"><div class="grabme"><img src="${grabmeIconUrl}"></div><div class="blockin">                  <div class="blockico"><span></span><img src="${logIconUrl}"></div><div class="blocktext">                        <p class="blocktitle">Update logs</p><p class="blockdesc">Edits and deletes log entries in this project</p>        </div></div></div><div class="blockelem create-flowy noselect"><input type="hidden" name="blockelemtype" class="blockelemtype" value="11"><div class="grabme"><img src="${grabmeIconUrl}"></div><div class="blockin">                  <div class="blockico"><span></span><img src="${errorIconUrl}"></div><div class="blocktext">                        <p class="blocktitle">Prompt an error</p><p class="blockdesc">Triggers a specified error</p>        </div></div></div>';
        }
    }
    addEventListenerMulti("click", disabledClick, false, ".side");
    document.getElementById("close").addEventListener("click", function () {
        if (rightcard) {
            rightcard = false;
            document.getElementById("properties").classList.remove("expanded");
            setTimeout(function () {
                document.getElementById("propwrap").classList.remove("itson");
            }, 300);
            tempblock.classList.remove("selectedblock");
        }
    });

    // document.getElementById("removeblock").addEventListener("click", function () {
    //     flowy.deleteBlocks();
    // });
    var aclick = false;
    var noinfo = false;
    var beginTouch = function (event) {
        aclick = true;
        noinfo = false;
        if (event.target.closest(".create-flowy")) {
            noinfo = true;
        }
    }
    var checkTouch = function (event) {
        aclick = false;
    }
    var doneTouch = function (event) {
        if (event.type === "mouseup" && aclick && !noinfo) {
            if (!rightcard && event.target.closest(".block") && !event.target.closest(".block").classList.contains("dragging")) {
                tempblock = event.target.closest(".block");
                rightcard = true;
                document.getElementById("properties").classList.add("expanded");
                document.getElementById("propwrap").classList.add("itson");
                tempblock.classList.add("selectedblock");

                var cardData = {
                    id: tempblock.querySelector('input[name="blockid"]').value,
                    temptype: tempblock.querySelector('input[name="blockelemtype"]').value,
                };
                // document.getElementById("removeblock").onclick = function () {
                //     deleteBlock(cardData.id);
                // };
            }
        }
    };
    function deleteBlock(blockId) {
        console.log(document.querySelector('input[name="blockid"][value="' + blockId + '"]'));

        var blockToRemove = document.querySelector('input[name="blockid"][value="' + blockId + '"]').closest('.block');
        if (blockToRemove) {
            blockToRemove.remove();
        }
        document.getElementById("properties").classList.remove("expanded");
        document.getElementById("propwrap").classList.remove("itson");
        rightcard = false;
    }


    addEventListener("mousedown", beginTouch, false);
    addEventListener("mousemove", checkTouch, false);
    addEventListener("mouseup", doneTouch, false);
    addEventListenerMulti("touchstart", beginTouch, false, ".block");
});
