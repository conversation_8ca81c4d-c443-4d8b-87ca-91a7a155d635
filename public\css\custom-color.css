body.custom-color {
    background: linear-gradient(141.55deg, rgba(240, 244, 243, 0) 3.46%, #f0f4f3 99.86%);
}
body.custom-color .bg-primary {
    background: linear-gradient(141.55deg, var(--color-customColor) 3.46%, var(--color-customColor) 99.86%), var(--color-customColor) !important;
}

body.custom-color .dash-header .drp-language .drp-text,
  body.custom-color .dash-header .dash-head-link > i:not(.nocolor) {
    color: var(--color-customColor);
}

body.custom-color .dash-header .drp-company .theme-avtar {
    background: #e9d5e7;
    color: var(--color-customColor);
}

body.custom-color .dash-sidebar.light-sidebar .dash-navbar > .dash-item.active > .dash-link, body.custom-color .dash-sidebar.light-sidebar .dash-navbar > .dash-item:active > .dash-link, body.custom-color .dash-sidebar.light-sidebar .dash-navbar > .dash-item:focus > .dash-link, body.custom-color .dash-sidebar.light-sidebar .dash-navbar > .dash-item:hover > .dash-link,
  body.custom-color .dash-sidebar .dash-navbar > .dash-item.active > .dash-link,
  body.custom-color .dash-sidebar .dash-navbar > .dash-item:active > .dash-link,
  body.custom-color .dash-sidebar .dash-navbar > .dash-item:focus > .dash-link,
  body.custom-color .dash-sidebar .dash-navbar > .dash-item:hover > .dash-link {
    background: linear-gradient(141.55deg, var(--color-customColor) 3.46%, var(--color-customColor) 99.86%), var(--color-customColor);;
    color: #fff;
    box-shadow: 0 5px 7px -1px rgba(146, 44, 136, 0.3);
}

body.custom-color .dash-sidebar.light-sidebar .dash-navbar > .dash-item.active > .dash-link i, body.custom-color .dash-sidebar.light-sidebar .dash-navbar > .dash-item:active > .dash-link i, body.custom-color .dash-sidebar.light-sidebar .dash-navbar > .dash-item:focus > .dash-link i, body.custom-color .dash-sidebar.light-sidebar .dash-navbar > .dash-item:hover > .dash-link i,
    body.custom-color .dash-sidebar .dash-navbar > .dash-item.active > .dash-link i,
    body.custom-color .dash-sidebar .dash-navbar > .dash-item:active > .dash-link i,
    body.custom-color .dash-sidebar .dash-navbar > .dash-item:focus > .dash-link i,
    body.custom-color .dash-sidebar .dash-navbar > .dash-item:hover > .dash-link i {
    color: var(--color-customColor);;
}

body.custom-color .dash-sidebar.light-sidebar .dash-item .dash-submenu .dash-item::before,
  body.custom-color .dash-sidebar .dash-item .dash-submenu .dash-item::before {
    border-right-color: var(--color-customColor);
}

body.custom-color .dash-sidebar.light-sidebar .dash-item .dash-submenu .dash-item.active > .dash-link, body.custom-color .dash-sidebar.light-sidebar .dash-item .dash-submenu .dash-item:active > .dash-link, body.custom-color .dash-sidebar.light-sidebar .dash-item .dash-submenu .dash-item:focus > .dash-link, body.custom-color .dash-sidebar.light-sidebar .dash-item .dash-submenu .dash-item:hover > .dash-link,
  body.custom-color .dash-sidebar .dash-item .dash-submenu .dash-item.active > .dash-link,
  body.custom-color .dash-sidebar .dash-item .dash-submenu .dash-item:active > .dash-link,
  body.custom-color .dash-sidebar .dash-item .dash-submenu .dash-item:focus > .dash-link,
  body.custom-color .dash-sidebar .dash-item .dash-submenu .dash-item:hover > .dash-link {
    color: var(--color-customColor);
}

body.custom-color a:not([class]) {
    color: var(--color-customColor);
}

body.custom-color a:not([class]):hover {
    color: var(--color-customColor);
}

body.custom-color .text-primary {
    color: var(--color-customColor) !important;
}

body.custom-color .list-group-item.active {
    background: linear-gradient(141.55deg, var(--color-customColor) 3.46%, var(--color-customColor) 99.86%), var(--color-customColor) !important;
}

body.custom-color .progress-bar:not([class*="bg-"]),
  body.custom-color .btn-primary {
    --bs-btn-color: #ffffff;
    --bs-btn-bg: var(--color-customColor);
    --bs-btn-border-color: var(--color-customColor);
    --bs-btn-hover-color: #ffffff;
    --bs-btn-hover-bg: var(--color-customColor);
    --bs-btn-hover-border-color: var(--color-customColor);
    --bs-btn-focus-shadow-rgb: 162, 76, 154;
    --bs-btn-active-color: #ffffff;
    --bs-btn-active-bg: var(--color-customColor);
    --bs-btn-active-border-color: var(--color-customColor);
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    --bs-btn-disabled-color: #ffffff;
    --bs-btn-disabled-bg: var(--color-customColor);;
    --bs-btn-disabled-border-color: var(--color-customColor);
}

body.custom-color .btn-outline-primary {
    --bs-btn-color: var(--color-customColor);
    --bs-btn-border-color: var(--color-customColor);
    --bs-btn-hover-color: #ffffff;
    --bs-btn-hover-bg: var(--color-customColor);
    --bs-btn-hover-border-color: var(--color-customColor);
    --bs-btn-focus-shadow-rgb: 146, 44, 136;
    --bs-btn-active-color: #ffffff;
    --bs-btn-active-bg: var(--color-customColor);
    --bs-btn-active-border-color: var(--color-customColor);
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    --bs-btn-disabled-color: var(--color-customColor);
    --bs-btn-disabled-bg: transparent;
    --bs-btn-disabled-border-color: var(--color-customColor);
    --bs-gradient: none;
}

body.custom-color .btn-light-primary {
    background: #e9d5e7;
    color: var(--color-customColor);
    border-color: #e9d5e7;
}

body.custom-color .btn-light-primary:hover {
    background: var(--color-customColor);
    color: #fff;
    border-color: var(--color-customColor);
}

body.custom-color .btn-light-primary.focus, body.custom-color .btn-light-primary:focus {
    background: var(--color-customColor);
    color: #fff;
    border-color: var(--color-customColor);
}

body.custom-color .btn-light-primary:not(:disabled):not(.disabled).active, body.custom-color .btn-light-primary:not(:disabled):not(.disabled):active,
    .show > body.custom-color .btn-light-primary.dropdown-toggle {
    background: var(--color-customColor);
    color: #fff;
    border-color: var(--color-customColor);
}

body.custom-color .btn-check:active + .btn-light-primary,
  body.custom-color .btn-check:checked + .btn-light-primary {
    background: var(--color-customColor);
    color: #fff;
    border-color: var(--color-customColor);
}

body.custom-color .alert-primary {
    --bs-alert-color: var(--color-customColor);
    --bs-alert-bg: #e9d5e7;
    --bs-alert-border-color: #dec0db;
}

body.custom-color .alert-primary .alert-link {
    color: #461542;
}

body.custom-color .badge.bg-light-primary {
    background: #e9d5e7;
    color: var(--color-customColor);
    border-color: #e9d5e7;
}

body.custom-color .page-item.active .page-link {
    background: var(--color-customColor);
    border-color: var(--color-customColor);
}

body.custom-color .nav-link {
    color: var(--color-customColor);
}

body.custom-color .nav-link:hover, body.custom-color .nav-link:focus {
    color: var(--color-customColor);
}

body.custom-color .nav-tabs .nav-link {
    background: none;
}

body.custom-color .nav-pills .nav-link {
    background: none;
}

body.custom-color .nav-pills .nav-link:focus,
  body.custom-color .nav-pills .nav-link.active,
  body.custom-color .nav-pills .show > .nav-link {
    color: #ffffff;
    background: linear-gradient(141.55deg, var(--color-customColor) 3.46%, var(--color-customColor) 99.86%), var(--color-customColor);
}

body.custom-color .form-check-input:focus,
  body.custom-color .form-select:focus,
  body.custom-color .form-control:focus,
  body.custom-color .custom-select:focus,
  body.custom-color .dataTable-selector:focus,
  body.custom-color .dataTable-input:focus {
    border-color: var(--color-customColor);
    box-shadow: 0 0 0 0.2rem var(--color-customColor);
}

body.custom-color .form-check-input:checked {
    background-color: var(--color-customColor);
    border-color: var(--color-customColor);
}

body.custom-color .form-range::-webkit-slider-thumb {
    background: var(--color-customColor);
}

body.custom-color .form-range::-webkit-slider-thumb:active {
    background: var(--color-customColor);
}

body.custom-color .form-range::-moz-range-thumb {
    background: var(--color-customColor);
}

body.custom-color .form-range::-moz-range-thumb:active {
    background: var(--color-customColor);
}

body.custom-color .form-check-input:checked {
    background-color: var(--color-customColor);
    border-color: var(--color-customColor);
}

body.custom-color .form-check-input[type="checkbox"]:indeterminate {
    background-color: var(--color-customColor);
    border-color: var(--color-customColor);
}

body.custom-color .form-check .form-check-input.input-primary:checked {
    background-color: var(--color-customColor);
    border-color: var(--color-customColor);
}

body.custom-color pre[class*="language-"] > code {
    border-left-color: var(--color-customColor);
    background-image: linear-gradient(transparent 50%, rgba(146, 44, 136, 0.04) 50%);
}

body.custom-color .card .card-header h5:after, body.custom-color .card .card-header .h5:after,
  body.custom-color .card .card-header .h5:after {
    background: var(--color-customColor);
}