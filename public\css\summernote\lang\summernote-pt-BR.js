(function($) {
  $.extend($.summernote.lang, {
    'pt-BR': {
      font: {
        bold: 'Negrito',
        italic: 'Itálico',
        underline: 'Sublinhado',
        clear: 'Remover estilo da fonte',
        height: '<PERSON><PERSON> da linha',
        name: '<PERSON><PERSON>',
        strikethrough: 'Risca<PERSON>',
        subscript: 'Subscript',
        superscript: 'Superscript',
        size: '<PERSON>an<PERSON> da fonte'
      },
      image: {
        image: 'Imagem',
        insert: 'Inserir imagem',
        resizeFull: 'Redimensionar Completamente',
        resizeHalf: 'Redimensionar pela Metade',
        resizeQuarter: 'Redimensionar um Quarto',
        floatLeft: 'Flutuar para Esquerda',
        floatRight: 'Flutuar para Direira',
        floatNone: 'Não Flutuar',
        shapeRounded: 'Shape: Rounded',
        shapeCircle: 'Shape: Circle',
        shapeThumbnail: 'Shape: Thumbnail',
        shapeNone: 'Shape: None',
        dragImageHere: 'Arraste uma imagem para cá',
        dropImage: 'Drop image or Text',
        selectFromFiles: 'Selecione a partir dos arquivos',
        maximumFileSize: 'Maximum file size',
        maximumFileSizeError: 'Maximum file size exceeded.',
        url: 'URL da imagem',
        remove: 'Remove Image',
        original: 'Original'
      },
      video: {
        video: 'Vídeo',
        videoLink: 'Link para vídeo',
        insert: 'Inserir vídeo',
        url: 'URL do vídeo?',
        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion ou Youku)'
      },
      link: {
        link: 'Link',
        insert: 'Inserir link',
        unlink: 'Remover link',
        edit: 'Editar',
        textToDisplay: 'Texto para exibir',
        url: 'Para qual URL esse link leva?',
        openInNewWindow: 'Abrir em uma nova janela'
      },
      table: {
        table: 'Tabela',
        addRowAbove: 'Adicionar linha acima',
        addRowBelow: 'Adicionar linha abaixo',
        addColLeft: 'Adicionar coluna a esquerda',
        addColRight: 'Adicionar coluna a direita',
        delRow: 'Excluir linha',
        delCol: 'Excluir coluna',
        delTable: 'Delete table'
      },
      hr: {
        insert: 'Inserir linha horizontal'
      },
      style: {
        style: 'Estilo',
        normal: 'Normal',
        blockquote: 'Citação',
        pre: 'Código',
        h1: 'Título 1',
        h2: 'Título 2',
        h3: 'Título 3',
        h4: 'Título 4',
        h5: 'Título 5',
        h6: 'Título 6'
      },
      lists: {
        unordered: 'Lista com marcadores',
        ordered: 'Lista numerada'
      },
      options: {
        help: 'Ajuda',
        fullscreen: 'Tela cheia',
        codeview: 'Ver código-fonte'
      },
      paragraph: {
        paragraph: 'Parágrafo',
        outdent: 'Menor tabulação',
        indent: 'Maior tabulação',
        left: 'Alinhar à esquerda',
        center: 'Alinhar ao centro',
        right: 'Alinha à direita',
        justify: 'Justificado'
      },
      color: {
        recent: 'Cor recente',
        more: 'Mais cores',
        background: 'Fundo',
        foreground: 'Fonte',
        transparent: 'Transparente',
        setTransparent: 'Fundo transparente',
        reset: 'Restaurar',
        resetToDefault: 'Restaurar padrão'
      },
      shortcut: {
        shortcuts: 'Atalhos do teclado',
        close: 'Fechar',
        textFormatting: 'Formatação de texto',
        action: 'Ação',
        paragraphFormatting: 'Formatação de parágrafo',
        documentStyle: 'Estilo de documento',
        extraKeys: 'Extra keys'
      },
      help: {
        'insertParagraph': 'Inserir Parágrafo',
        'undo': 'Desfazer o último comando',
        'redo': 'Refazer o último comando',
        'tab': 'Tab',
        'untab': 'Desfazer tab',
        'bold': 'Colocar em negrito',
        'italic': 'Colocar em itálico',
        'underline': 'Sublinhado',
        'strikethrough': 'Tachado',
        'removeFormat': 'Remover estilo',
        'justifyLeft': 'Alinhar à esquerda',
        'justifyCenter': 'Centralizar',
        'justifyRight': 'Alinhar à esquerda',
        'justifyFull': 'Justificar',
        'insertUnorderedList': 'Lista não ordenada',
        'insertOrderedList': 'Lista ordenada',
        'outdent': 'Recuar parágrafo atual',
        'indent': 'Avançar parágrafo atual',
        'formatPara': 'Alterar formato do bloco para parágrafo(tag P)',
        'formatH1': 'Alterar formato do bloco para H1',
        'formatH2': 'Alterar formato do bloco para H2',
        'formatH3': 'Alterar formato do bloco para H3',
        'formatH4': 'Alterar formato do bloco para H4',
        'formatH5': 'Alterar formato do bloco para H5',
        'formatH6': 'Alterar formato do bloco para H6',
        'insertHorizontalRule': 'Inserir régua horizontal',
        'linkDialog.show': 'Inserir um Hiperlink'
      },
      history: {
        undo: 'Desfazer',
        redo: 'Refazer'
      },
      specialChar: {
        specialChar: 'SPECIAL CHARACTERS',
        select: 'Select Special characters'
      }
    }
  });
})(jQuery);
