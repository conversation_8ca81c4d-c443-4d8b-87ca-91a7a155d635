<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\GlobalScoutService;

class GlobalIndexCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'global:index';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Index the entire database into TNTSearch';

    /**
     * Execute the console command.
     */
    public function handle(GlobalScoutService $service)
    {
        $indexed = $service->indexWholeDatabase();
        foreach ($indexed as $table => $count) {
            $this->info("Indexed {$count} rows from {$table}");
        }
    }
}
