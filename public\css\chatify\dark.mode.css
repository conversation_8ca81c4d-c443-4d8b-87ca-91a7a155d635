/*app scroll*/
.app-scroll::-webkit-scrollbar-thumb,
.app-scroll-thin::-webkit-scrollbar-thumb{
    background:#323232;
}
.app-scroll-thin::-webkit-scrollbar{
    background: #2c2c2c;
}
.app-scroll::-webkit-scrollbar:hover,
.app-scroll-thin::-webkit-scrollbar:hover{
    background: #2c2c2c;
}

.messenger{
    /*background: #272727;*/
}
.messenger-search{
	background: #11263f;
	color: #fff;
}
.messenger-listView{
	background: #11263f;
  	border: 1px solid #323232;
}
.messenger-listView-tabs{
	border-bottom: 1px solid #323232;
}
.messenger-listView-tabs a:hover,
.messenger-listView-tabs a:focus{
	background-color: #323232;
}
.messenger-favorites div.avatar{
    border: 2px solid #272727;
}
.messenger-list-item:hover{
	background: #323232;
}
.messenger-messagingView {
    border-top: 1px solid #323232;
    border-bottom: 1px solid #323232;
    background: #11263f;
}
.m-header-messaging {
    background: #11263f;
}
.messenger-infoView {
    background: #11263f;
    border: 1px solid #323232;
}
.messenger-infoView>p{
  color: #fff;
}
.divider{
    border-top: 1px solid #2f2f2f;
}
.messenger-sendCard{
	background: #11263f;
	border-top: 1px solid #323232;
}
.attachment-preview>p{
    color: #fff;
}
.m-send{
	color: #fff;
}
.message-card p{
    background: #1a3350;
    color: #fff;
}
.m-li-divider{
    border-bottom: 1px solid #323232;
}
.m-header a,
.m-header a:hover,
.m-header a:focus{
	text-decoration: none;
	color: #fff;
}
.messenger-list-item td p{
    color: #fff;
}
.activeStatus{
    border: 2px solid #272727;
    z-index: 9;
}
.messenger-list-item:hover .activeStatus{
    border-color: #323232;
}
.messenger-favorites>div p{
    color: #ffffff;
}


/*
***********************************************
* Placeholder loading
***********************************************
*/
.loadingPlaceholder-body div,
.loadingPlaceholder-header tr td div {
	background: #323232;
    background-image: -webkit-linear-gradient(left, #323232 0%, #323232 20%, #323232 40%, #323232 100%);
}

/*
***********************************************
* App Modal
***********************************************
*/

.app-modal-card{
	background: rgba(50, 50, 50, 0.94);
}
.app-modal-header{
 	color: #fff;
}
.app-modal-body{
  color: #fff;
}



