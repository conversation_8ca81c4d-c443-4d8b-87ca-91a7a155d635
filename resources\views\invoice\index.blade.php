@extends('layouts.admin')
@section('page-title')
    {{ __('Manage Invoices') }}
@endsection
@push('css-page')
    <link rel="stylesheet" href="{{ asset('css/datatable/buttons.dataTables.min.css') }}">
    <style>
        .invoice-metric {
            text-align: center;
            padding: 0 15px;
        }

        .metric-amount {
            font-size: 1.5rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 4px;
        }

        .metric-description {
            font-size: 0.875rem;
            line-height: 1.2;
        }

        .invoice-progress-container {
            margin: 20px 0 0 0;
        }

        .progress-bar-custom {
            display: flex;
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
            background-color: #e9ecef;
        }

        .progress-segment {
            min-width: 2px;
            transition: all 0.3s ease;
        }

        .progress-segment:not(:last-child) {
            margin-right: 1px;
        }

        .bg-purple {
            background-color: #6f42c1 !important;
        }

        @media (max-width: 768px) {
            .invoice-metric {
                padding: 0 8px;
                margin-bottom: 15px;
            }

            .metric-amount {
                font-size: 1.25rem;
            }

            .metric-description {
                font-size: 0.8rem;
            }
        }
    </style>
@endpush

@push('script-page')
    <script>
        function copyToClipboard(element) {

            var copyText = element.id;
            navigator.clipboard.writeText(copyText);
            // document.addEventListener('copy', function (e) {
            //     e.clipboardData.setData('text/plain', copyText);
            //     e.preventDefault();
            // }, true);
            //
            // document.execCommand('copy');
            show_toastr('success', 'Url copied to clipboard', 'success');
        }
    </script>
@endpush


@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item">{{ __('Invoice') }}</li>
@endsection

@section('action-btn')
    <div class="float-end">
        {{--        <a class="btn btn-sm btn-primary" data-bs-toggle="collapse" href="#multiCollapseExample1" role="button" aria-expanded="false" aria-controls="multiCollapseExample1" data-bs-toggle="tooltip" title="{{__('Filter')}}"> --}}
        {{--            <i class="ti ti-filter"></i> --}}
        {{--        </a> --}}

        {{-- <a href="{{ route('invoice.export') }}" class="btn btn-sm btn-primary" data-bs-toggle="tooltip"
            title="{{ __('Export') }}">
            <i class="ti ti-file-export"></i>
        </a> --}}

        @can('create invoice')
            <a href="{{ route('invoice.create', 0) }}" class="btn btn-sm btn-primary" data-bs-toggle="tooltip"
                title="{{ __('Create') }}">
                <i class="ti ti-plus"></i>
            </a>
        @endcan
    </div>
@endsection



@section('content')
    {{-- Include Sales Tabs --}}
    @include('transaction.sales-tabs')

    {{-- bars --}}
    <div class="accordion mb-3" id="invoiceAccordion">
        <div class="accordion-item">
            <h2 class="accordion-header" id="invoiceHeading">
                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                    data-bs-target="#invoiceCollapse" aria-expanded="true" aria-controls="invoiceCollapse">
                    <div class="d-flex justify-content-between align-items-center w-100 me-3">
                        <span class="fw-bold">{{ __('Invoice Summary') }}</span>
                        <div class="d-flex align-items-center gap-2">
                            <span class="text-primary small">{{ __('Overview') }}</span>
                            <i class="bi bi-file-earmark-text text-primary"></i>
                        </div>
                    </div>
                </button>
            </h2>
            <div id="invoiceCollapse" class="accordion-collapse collapse show" aria-labelledby="invoiceHeading"
                data-bs-parent="#invoiceAccordion">
                <div class="accordion-body p-4">

                    {{-- Invoice Metrics Row --}}
                    <div class="row g-0 mb-3">
                        {{-- Draft --}}
                        <div class="col">
                            <div class="invoice-metric">
                                <div class="metric-amount">
                                    {{ Auth::user()->priceFormat($invoiceData['draft']['amount'] ?? 0) }}
                                </div>
                                <div class="metric-description text-muted small">
                                    {{ $invoiceData['draft']['count'] ?? 0 }} {{ __('Draft') }}
                                </div>
                            </div>
                        </div>

                        {{-- Sent --}}
                        <div class="col">
                            <div class="invoice-metric">
                                <div class="metric-amount">
                                    {{ Auth::user()->priceFormat($invoiceData['sent']['amount'] ?? 0) }}
                                </div>
                                <div class="metric-description text-muted small">
                                    {{ $invoiceData['sent']['count'] ?? 0 }} {{ __('Sent') }}
                                </div>
                            </div>
                        </div>

                        {{-- Overdue --}}
                        <div class="col">
                            <div class="invoice-metric">
                                <div class="metric-amount">
                                    {{ Auth::user()->priceFormat($invoiceData['overdue']['amount'] ?? 0) }}
                                </div>
                                <div class="metric-description text-muted small">
                                    {{ $invoiceData['overdue']['count'] ?? 0 }} {{ __('Overdue') }}
                                </div>
                            </div>
                        </div>

                        {{-- Unpaid --}}
                        <div class="col">
                            <div class="invoice-metric">
                                <div class="metric-amount">
                                    {{ Auth::user()->priceFormat($invoiceData['unpaid']['amount'] ?? 0) }}
                                </div>
                                <div class="metric-description text-muted small">
                                    {{ $invoiceData['unpaid']['count'] ?? 0 }} {{ __('Unpaid') }}
                                </div>
                            </div>
                        </div>

                        {{-- Partially Paid --}}
                        <div class="col">
                            <div class="invoice-metric">
                                <div class="metric-amount">
                                    {{ Auth::user()->priceFormat($invoiceData['partially_paid']['amount'] ?? 0) }}
                                </div>
                                <div class="metric-description text-muted small">
                                    {{ $invoiceData['partially_paid']['count'] ?? 0 }} {{ __('Partially Paid') }}
                                </div>
                            </div>
                        </div>

                        {{-- Paid --}}
                        <div class="col">
                            <div class="invoice-metric">
                                <div class="metric-amount">
                                    {{ Auth::user()->priceFormat($invoiceData['paid']['amount'] ?? 0) }}
                                </div>
                                <div class="metric-description text-muted small">
                                    {{ $invoiceData['paid']['count'] ?? 0 }} {{ __('Paid') }}
                                </div>
                            </div>
                        </div>
                    </div>

                    {{-- Color Progress Bars --}}
                    <div class="invoice-progress-container">
                        <div class="progress-bar-custom">
                            <div class="progress-segment bg-secondary"
                                style="flex: {{ max(1, $invoiceData['draft']['amount'] ?? 0) }};"></div>
                            <div class="progress-segment bg-info"
                                style="flex: {{ max(1, $invoiceData['sent']['amount'] ?? 0) }};"></div>
                            <div class="progress-segment bg-warning"
                                style="flex: {{ max(1, $invoiceData['overdue']['amount'] ?? 0) }};"></div>
                            <div class="progress-segment bg-danger"
                                style="flex: {{ max(1, $invoiceData['unpaid']['amount'] ?? 0) }};"></div>
                            <div class="progress-segment bg-purple"
                                style="flex: {{ max(1, $invoiceData['partially_paid']['amount'] ?? 0) }};"></div>
                            <div class="progress-segment bg-success"
                                style="flex: {{ max(1, $invoiceData['paid']['amount'] ?? 0) }};"></div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

        <div class="row">
            <div class="col-sm-12">
                <div class="mt-2 mb-2" id="multiCollapseExample1">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="card-body">
                            {{ Form::open(['route' => ['invoice.index'], 'method' => 'GET', 'id' => 'customer_submit']) }}
                            <div class="row d-flex align-items-center justify-content-start">
                                <div class="col-xl-3 col-lg-3 col-md-6 col-sm-12 col-12 mr-2">
                                    <div class="btn-box">
                                        {{ Form::label('issue_date', __('Issue Date'), ['class' => 'form-label']) }}
                                        {{ Form::date('issue_date', isset($_GET['issue_date']) ? $_GET['issue_date'] : '', ['class' => 'form-control month-btn', 'id' => 'pc-daterangepicker-1']) }}
                                    </div>
                                </div>
                                <div class="col-xl-3 col-lg-3 col-md-6 col-sm-12 col-12 mr-2">
                                    <div class="btn-box">
                                        {{ Form::label('customer', __('Customer'), ['class' => 'form-label']) }}
                                        {{ Form::select('customer', $customer, isset($_GET['customer']) ? $_GET['customer'] : '', ['class' => 'form-control select auto-filter']) }}
                                    </div>
                                </div>
                                <div class="col-xl-3 col-lg-3 col-md-6 col-sm-12 col-12">
                                    <div class="btn-box">
                                        {{ Form::label('status', __('Status'), ['class' => 'form-label']) }}
                                        {{ Form::select('status', ['' => 'Select Status'] + $status, isset($_GET['status']) ? $_GET['status'] : '', ['class' => 'form-control select auto-filter']) }}
                                    </div>
                                </div>
                                {{-- <div class="col-auto float-end ms-2 mt-4">
                                <a href="#" class="btn btn-sm btn-primary"
                                    onclick="document.getElementById('customer_submit').submit(); return false;"
                                    data-toggle="tooltip" data-original-title="{{ __('apply') }}">
                                    <span class="btn-inner--icon"><i class="ti ti-search"></i></span>
                                </a>
                                <a href="{{ route('invoice.index') }}" class="btn btn-sm btn-danger" data-toggle="tooltip"
                                    data-original-title="{{ __('Reset') }}">
                                    <span class="btn-inner--icon"><i class="ti ti-trash-off text-white-off"></i></span>
                                </a>
                            </div> --}}
                            </div>
                            {{ Form::close() }}
                        </div>
                        <div class="col-auto mt-4">
                            @can('create invoice')
                                <a href="{{ route('invoice.create', 0) }}" class="btn btn-sm btn-primary"
                                    data-bs-toggle="tooltip" title="{{ __('Create') }}">
                                    {{ __('Create Invoice') }}
                                    <i class="ti ti-plus"></i>
                                </a>
                            @endcan
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Auto-submit form when select filters change (customer, status)
                const selectFilterElements = document.querySelectorAll('select.auto-filter');

                selectFilterElements.forEach(function(element) {
                    element.addEventListener('change', function() {
                        document.getElementById('customer_submit').submit();
                    });
                });

                // Handle date field with delay to allow proper date selection
                const dateField = document.getElementById('pc-daterangepicker-1');
                let dateTimeout;

                if (dateField) {
                    dateField.addEventListener('change', function() {
                        // Clear any existing timeout
                        clearTimeout(dateTimeout);

                        // Set a delay to allow user to finish selecting date
                        dateTimeout = setTimeout(function() {
                            document.getElementById('customer_submit').submit();
                        }, 1000); // 1 second delay
                    });

                    // Also submit when user clicks away from the date field (blur event)
                    dateField.addEventListener('blur', function() {
                        clearTimeout(dateTimeout);
                        if (this.value) {
                            document.getElementById('customer_submit').submit();
                        }
                    });
                }
            });
        </script>

        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body table-border-style">
                        <h5></h5>
                        <div class="table-responsive">
                            <table class="table datatable">
                                <thead>
                                    <tr>
                                        <th> {{ __('Invoice') }}</th>
                                        {{--                                @if (!\Auth::guard('customer')->check()) --}}
                                        {{--                                    <th>{{ __('Customer') }}</th> --}}
                                        {{--                                @endif --}}
                                        <th>{{ __('Issue Date') }}</th>
                                        <th>{{ __('Due Date') }}</th>
                                        <th>{{ __('Due Amount') }}</th>
                                        <th>{{ __('Status') }}</th>
                                        @if (Gate::check('edit invoice') || Gate::check('delete invoice') || Gate::check('show invoice'))
                                            <th>{{ __('Action') }}</th>
                                        @endif
                                        {{-- <th>
                                <td class="barcode">
                                    {!! DNS1D::getBarcodeHTML($invoice->sku, "C128",1.4,22) !!}
                                    <p class="pid">{{$invoice->sku}}</p>
                                </td>
                            </th> --}}
                                    </tr>
                                </thead>

                                <tbody>
                                    @foreach ($invoices as $invoice)
                                        <tr>
                                            <td class="Id">
                                                <a href="{{ route('invoice.show', \Crypt::encrypt($invoice->id)) }}"
                                                    style="padding-top: 11px;"
                                                    class="btn btn-outline-primary">{{ AUth::user()->invoiceNumberFormat($invoice->invoice_id) }}
                                                    @if ($invoice->updated_at < \Carbon\Carbon::now()->subHours(96) && $invoice->status != 4)
                                                        <small
                                                            style="position: absolute; position: absolute; margin-top: -15px; margin-left: -24px;"><span
                                                                class="badge bg-danger" data-bs-toggle="tooltip"
                                                                title="{{ __('No Activity for the Last 96 Hours ') }}">
                                                                {{ __('Follow Up') }}</span></small>
                                                </a>
                                    @endif
                                    </td>
                                    <td>{{ Auth::user()->dateFormat($invoice->issue_date) }}</td>
                                    <td>
                                        @if ($invoice->due_date < date('Y-m-d'))
                                            <p class="text-danger mt-3">
                                                {{ \Auth::user()->dateFormat($invoice->due_date) }}</p>
                                        @else
                                            {{ \Auth::user()->dateFormat($invoice->due_date) }}
                                        @endif
                                    </td>
                                    <td>{{ \Auth::user()->priceFormat($invoice->getDue()) }}</td>
                                    <td>
                                        @if ($invoice->status == 0)
                                            <span
                                                class="status_badge badge bg-secondary p-2 px-3 rounded">{{ __(\App\Models\Invoice::$statues[$invoice->status]) }}</span>
                                        @elseif($invoice->status == 1)
                                            <span
                                                class="status_badge badge bg-warning p-2 px-3 rounded">{{ __(\App\Models\Invoice::$statues[$invoice->status]) }}</span>
                                        @elseif($invoice->status == 2)
                                            <span
                                                class="status_badge badge bg-danger p-2 px-3 rounded">{{ __(\App\Models\Invoice::$statues[$invoice->status]) }}</span>
                                        @elseif($invoice->status == 3)
                                            <span
                                                class="status_badge badge bg-info p-2 px-3 rounded">{{ __(\App\Models\Invoice::$statues[$invoice->status]) }}</span>
                                        @elseif($invoice->status == 4)
                                            <span
                                                class="status_badge badge bg-primary p-2 px-3 rounded">{{ __(\App\Models\Invoice::$statues[$invoice->status]) }}</span>
                                        @endif
                                    </td>
                                    @if (Gate::check('edit invoice') || Gate::check('delete invoice') || Gate::check('show invoice'))
                                        <td class="Action">
                                            <span>
                                                @php $invoiceID= Crypt::encrypt($invoice->id); @endphp

                                                @can('copy invoice')
                                                    <div class="action-btn bg-warning ms-2">
                                                        <a href="#" id="{{ route('invoice.link.copy', [$invoiceID]) }}"
                                                            class="mx-3 btn btn-sm align-items-center"
                                                            onclick="copyToClipboard(this)" data-bs-toggle="tooltip"
                                                            title="{{ __('Copy Invoice') }}"
                                                            data-original-title="{{ __('Copy Invoice') }}"><i
                                                                class="ti ti-link text-white"></i></a>
                                                    </div>
                                                @endcan
                                                @can('duplicate invoice')
                                                    <div class="action-btn bg-primary ms-2">
                                                        {!! Form::open([
                                                            'method' => 'get',
                                                            'route' => ['invoice.duplicate', $invoice->id],
                                                            'id' => 'duplicate-form-' . $invoice->id,
                                                        ]) !!}

                                                        <a href="#"
                                                            class="mx-3 btn btn-sm align-items-center bs-pass-para"
                                                            data-toggle="tooltip" data-original-title="{{ __('Duplicate') }}"
                                                            data-bs-toggle="tooltip" title="Duplicate Invoice"
                                                            data-original-title="{{ __('Delete') }}"
                                                            data-confirm="You want to confirm this action. Press Yes to continue or Cancel to go back"
                                                            data-confirm-yes="document.getElementById('duplicate-form-{{ $invoice->id }}').submit();">
                                                            <i class="ti ti-copy text-white"></i>
                                                            {!! Form::open([
                                                                'method' => 'get',
                                                                'route' => ['invoice.duplicate', $invoice->id],
                                                                'id' => 'duplicate-form-' . $invoice->id,
                                                            ]) !!}
                                                            {!! Form::close() !!}
                                                        </a>
                                                    </div>
                                                @endcan
                                                @can('show invoice')
                                                    {{--                                                        @if (\Auth::guard('customer')->check()) --}}
                                                    {{--                                                            <div class="action-btn bg-info ms-2"> --}}
                                                    {{--                                                                    <a href="{{ route('customer.invoice.show', \Crypt::encrypt($invoice->id)) }}" --}}
                                                    {{--                                                                       class="mx-3 btn btn-sm align-items-center" data-bs-toggle="tooltip" title="Show " --}}
                                                    {{--                                                                       data-original-title="{{ __('Detail') }}"> --}}
                                                    {{--                                                                        <i class="ti ti-eye text-white"></i> --}}
                                                    {{--                                                                    </a> --}}
                                                    {{--                                                                </div> --}}
                                                    {{--                                                        @else --}}
                                                    <div class="action-btn bg-info ms-2">
                                                        <a href="{{ route('invoice.show', \Crypt::encrypt($invoice->id)) }}"
                                                            class="mx-3 btn btn-sm align-items-center" data-bs-toggle="tooltip"
                                                            title="Show " data-original-title="{{ __('Detail') }}">
                                                            <i class="ti ti-eye text-white"></i>
                                                        </a>
                                                    </div>
                                                    {{--                                                        @endif --}}
                                                @endcan
                                                @can('edit invoice')
                                                    <div class="action-btn bg-primary ms-2">
                                                        <a href="{{ route('invoice.edit', \Crypt::encrypt($invoice->id)) }}"
                                                            class="mx-3 btn btn-sm align-items-center" data-bs-toggle="tooltip"
                                                            title="Edit " data-original-title="{{ __('Edit') }}">
                                                            <i class="ti ti-pencil text-white"></i>
                                                        </a>
                                                    </div>
                                                @endcan
                                                @can('delete invoice')
                                                    <div class="action-btn bg-danger ms-2">
                                                        {!! Form::open([
                                                            'method' => 'DELETE',
                                                            'route' => ['invoice.destroy', $invoice->id],
                                                            'id' => 'delete-form-' . $invoice->id,
                                                        ]) !!}
                                                        <a href="#"
                                                            class="mx-3 btn btn-sm align-items-center bs-pass-para "
                                                            data-bs-toggle="tooltip" title="{{ __('Delete') }}"
                                                            data-original-title="{{ __('Delete') }}"
                                                            data-confirm="{{ __('Are You Sure?') . '|' . __('This action can not be undone. Do you want to continue?') }}"
                                                            data-confirm-yes="document.getElementById('delete-form-{{ $invoice->id }}').submit();">
                                                            <i class="ti ti-trash text-white"></i>
                                                        </a>
                                                        {!! Form::close() !!}
                                                    </div>
                                                @endcan
                                            </span>
                                        </td>
                                    @endif
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endsection
