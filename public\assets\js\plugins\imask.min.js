!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).IMask={})}(this,(function(t){"use strict";var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},n=function(t){return t&&t.Math==Math&&t},i=n("object"==typeof globalThis&&globalThis)||n("object"==typeof window&&window)||n("object"==typeof self&&self)||n("object"==typeof e&&e)||function(){return this}()||Function("return this")(),r={},u=function(t){try{return!!t()}catch(t){return!0}},s=!u((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),a=!u((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),o=a,l=Function.prototype.call,h=o?l.bind(l):function(){return l.apply(l,arguments)},c={},f={}.propertyIsEnumerable,p=Object.getOwnPropertyDescriptor,d=p&&!f.call({1:2},1);c.f=d?function(t){var e=p(this,t);return!!e&&e.enumerable}:f;var v,g,k=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},y=a,m=Function.prototype,b=m.bind,_=m.call,A=y&&b.bind(_,_),C=y?function(t){return t&&A(t)}:function(t){return t&&function(){return _.apply(t,arguments)}},E=C,F=E({}.toString),S=E("".slice),w=function(t){return S(F(t),8,-1)},B=C,D=u,x=w,M=i.Object,O=B("".split),P=D((function(){return!M("z").propertyIsEnumerable(0)}))?function(t){return"String"==x(t)?O(t,""):M(t)}:M,T=i.TypeError,I=function(t){if(null==t)throw T("Can't call method on "+t);return t},j=P,R=I,V=function(t){return j(R(t))},L=function(t){return"function"==typeof t},N=L,U=function(t){return"object"==typeof t?null!==t:N(t)},z=i,q=L,H=function(t){return q(t)?t:void 0},Y=function(t,e){return arguments.length<2?H(z[t]):z[t]&&z[t][e]},Z=C({}.isPrototypeOf),G=Y("navigator","userAgent")||"",K=i,$=G,W=K.process,X=K.Deno,J=W&&W.versions||X&&X.version,Q=J&&J.v8;Q&&(g=(v=Q.split("."))[0]>0&&v[0]<4?1:+(v[0]+v[1])),!g&&$&&(!(v=$.match(/Edge\/(\d+)/))||v[1]>=74)&&(v=$.match(/Chrome\/(\d+)/))&&(g=+v[1]);var tt=g,et=u,nt=!!Object.getOwnPropertySymbols&&!et((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&tt&&tt<41})),it=nt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,rt=Y,ut=L,st=Z,at=it,ot=i.Object,lt=at?function(t){return"symbol"==typeof t}:function(t){var e=rt("Symbol");return ut(e)&&st(e.prototype,ot(t))},ht=i.String,ct=L,ft=function(t){try{return ht(t)}catch(t){return"Object"}},pt=i.TypeError,dt=function(t){if(ct(t))return t;throw pt(ft(t)+" is not a function")},vt=h,gt=L,kt=U,yt=i.TypeError,mt={exports:{}},bt=i,_t=Object.defineProperty,At=function(t,e){try{_t(bt,t,{value:e,configurable:!0,writable:!0})}catch(n){bt[t]=e}return e},Ct=At,Et="__core-js_shared__",Ft=i[Et]||Ct(Et,{}),St=Ft;(mt.exports=function(t,e){return St[t]||(St[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.21.0",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.21.0/LICENSE",source:"https://github.com/zloirock/core-js"});var wt=I,Bt=i.Object,Dt=function(t){return Bt(wt(t))},xt=Dt,Mt=C({}.hasOwnProperty),Ot=Object.hasOwn||function(t,e){return Mt(xt(t),e)},Pt=C,Tt=0,It=Math.random(),jt=Pt(1..toString),Rt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+jt(++Tt+It,36)},Vt=i,Lt=mt.exports,Nt=Ot,Ut=Rt,zt=nt,qt=it,Ht=Lt("wks"),Yt=Vt.Symbol,Zt=Yt&&Yt.for,Gt=qt?Yt:Yt&&Yt.withoutSetter||Ut,Kt=function(t){if(!Nt(Ht,t)||!zt&&"string"!=typeof Ht[t]){var e="Symbol."+t;zt&&Nt(Yt,t)?Ht[t]=Yt[t]:Ht[t]=qt&&Zt?Zt(e):Gt(e)}return Ht[t]},$t=h,Wt=U,Xt=lt,Jt=function(t,e){var n=t[e];return null==n?void 0:dt(n)},Qt=function(t,e){var n,i;if("string"===e&&gt(n=t.toString)&&!kt(i=vt(n,t)))return i;if(gt(n=t.valueOf)&&!kt(i=vt(n,t)))return i;if("string"!==e&&gt(n=t.toString)&&!kt(i=vt(n,t)))return i;throw yt("Can't convert object to primitive value")},te=Kt,ee=i.TypeError,ne=te("toPrimitive"),ie=function(t,e){if(!Wt(t)||Xt(t))return t;var n,i=Jt(t,ne);if(i){if(void 0===e&&(e="default"),n=$t(i,t,e),!Wt(n)||Xt(n))return n;throw ee("Can't convert object to primitive value")}return void 0===e&&(e="number"),Qt(t,e)},re=lt,ue=function(t){var e=ie(t,"string");return re(e)?e:e+""},se=U,ae=i.document,oe=se(ae)&&se(ae.createElement),le=function(t){return oe?ae.createElement(t):{}},he=!s&&!u((function(){return 7!=Object.defineProperty(le("div"),"a",{get:function(){return 7}}).a})),ce=s,fe=h,pe=c,de=k,ve=V,ge=ue,ke=Ot,ye=he,me=Object.getOwnPropertyDescriptor;r.f=ce?me:function(t,e){if(t=ve(t),e=ge(e),ye)try{return me(t,e)}catch(t){}if(ke(t,e))return de(!fe(pe.f,t,e),t[e])};var be={},_e=s&&u((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Ae=i,Ce=U,Ee=Ae.String,Fe=Ae.TypeError,Se=function(t){if(Ce(t))return t;throw Fe(Ee(t)+" is not an object")},we=s,Be=he,De=_e,xe=Se,Me=ue,Oe=i.TypeError,Pe=Object.defineProperty,Te=Object.getOwnPropertyDescriptor,Ie="enumerable",je="configurable",Re="writable";be.f=we?De?function(t,e,n){if(xe(t),e=Me(e),xe(n),"function"==typeof t&&"prototype"===e&&"value"in n&&Re in n&&!n.writable){var i=Te(t,e);i&&i.writable&&(t[e]=n.value,n={configurable:je in n?n.configurable:i.configurable,enumerable:Ie in n?n.enumerable:i.enumerable,writable:!1})}return Pe(t,e,n)}:Pe:function(t,e,n){if(xe(t),e=Me(e),xe(n),Be)try{return Pe(t,e,n)}catch(t){}if("get"in n||"set"in n)throw Oe("Accessors not supported");return"value"in n&&(t[e]=n.value),t};var Ve=be,Le=k,Ne=s?function(t,e,n){return Ve.f(t,e,Le(1,n))}:function(t,e,n){return t[e]=n,t},Ue={exports:{}},ze=L,qe=Ft,He=C(Function.toString);ze(qe.inspectSource)||(qe.inspectSource=function(t){return He(t)});var Ye,Ze,Ge,Ke=qe.inspectSource,$e=L,We=Ke,Xe=i.WeakMap,Je=$e(Xe)&&/native code/.test(We(Xe)),Qe=mt.exports,tn=Rt,en=Qe("keys"),nn={},rn=Je,un=i,sn=C,an=U,on=Ne,ln=Ot,hn=Ft,cn=function(t){return en[t]||(en[t]=tn(t))},fn=nn,pn="Object already initialized",dn=un.TypeError,vn=un.WeakMap;if(rn||hn.state){var gn=hn.state||(hn.state=new vn),kn=sn(gn.get),yn=sn(gn.has),mn=sn(gn.set);Ye=function(t,e){if(yn(gn,t))throw new dn(pn);return e.facade=t,mn(gn,t,e),e},Ze=function(t){return kn(gn,t)||{}},Ge=function(t){return yn(gn,t)}}else{var bn=cn("state");fn[bn]=!0,Ye=function(t,e){if(ln(t,bn))throw new dn(pn);return e.facade=t,on(t,bn,e),e},Ze=function(t){return ln(t,bn)?t[bn]:{}},Ge=function(t){return ln(t,bn)}}var _n={set:Ye,get:Ze,has:Ge,enforce:function(t){return Ge(t)?Ze(t):Ye(t,{})},getterFor:function(t){return function(e){var n;if(!an(e)||(n=Ze(e)).type!==t)throw dn("Incompatible receiver, "+t+" required");return n}}},An=s,Cn=Ot,En=Function.prototype,Fn=An&&Object.getOwnPropertyDescriptor,Sn=Cn(En,"name"),wn=Sn&&"something"===function(){}.name,Bn=Sn&&(!An||An&&Fn(En,"name").configurable),Dn=i,xn=L,Mn=Ot,On=Ne,Pn=At,Tn=Ke,In={EXISTS:Sn,PROPER:wn,CONFIGURABLE:Bn}.CONFIGURABLE,jn=_n.get,Rn=_n.enforce,Vn=String(String).split("String");(Ue.exports=function(t,e,n,i){var r,u=!!i&&!!i.unsafe,s=!!i&&!!i.enumerable,a=!!i&&!!i.noTargetGet,o=i&&void 0!==i.name?i.name:e;xn(n)&&("Symbol("===String(o).slice(0,7)&&(o="["+String(o).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!Mn(n,"name")||In&&n.name!==o)&&On(n,"name",o),(r=Rn(n)).source||(r.source=Vn.join("string"==typeof o?o:""))),t!==Dn?(u?!a&&t[e]&&(s=!0):delete t[e],s?t[e]=n:On(t,e,n)):s?t[e]=n:Pn(e,n)})(Function.prototype,"toString",(function(){return xn(this)&&jn(this).source||Tn(this)}));var Ln={},Nn=Math.ceil,Un=Math.floor,zn=function(t){var e=+t;return e!=e||0===e?0:(e>0?Un:Nn)(e)},qn=zn,Hn=Math.max,Yn=Math.min,Zn=zn,Gn=Math.min,Kn=function(t){return t>0?Gn(Zn(t),9007199254740991):0},$n=Kn,Wn=V,Xn=function(t,e){var n=qn(t);return n<0?Hn(n+e,0):Yn(n,e)},Jn=function(t){return $n(t.length)},Qn=function(t){return function(e,n,i){var r,u=Wn(e),s=Jn(u),a=Xn(i,s);if(t&&n!=n){for(;s>a;)if((r=u[a++])!=r)return!0}else for(;s>a;a++)if((t||a in u)&&u[a]===n)return t||a||0;return!t&&-1}},ti={includes:Qn(!0),indexOf:Qn(!1)},ei=Ot,ni=V,ii=ti.indexOf,ri=nn,ui=C([].push),si=function(t,e){var n,i=ni(t),r=0,u=[];for(n in i)!ei(ri,n)&&ei(i,n)&&ui(u,n);for(;e.length>r;)ei(i,n=e[r++])&&(~ii(u,n)||ui(u,n));return u},ai=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],oi=si,li=ai.concat("length","prototype");Ln.f=Object.getOwnPropertyNames||function(t){return oi(t,li)};var hi={};hi.f=Object.getOwnPropertySymbols;var ci=Y,fi=Ln,pi=hi,di=Se,vi=C([].concat),gi=ci("Reflect","ownKeys")||function(t){var e=fi.f(di(t)),n=pi.f;return n?vi(e,n(t)):e},ki=Ot,yi=gi,mi=r,bi=be,_i=u,Ai=L,Ci=/#|\.prototype\./,Ei=function(t,e){var n=Si[Fi(t)];return n==Bi||n!=wi&&(Ai(e)?_i(e):!!e)},Fi=Ei.normalize=function(t){return String(t).replace(Ci,".").toLowerCase()},Si=Ei.data={},wi=Ei.NATIVE="N",Bi=Ei.POLYFILL="P",Di=Ei,xi=i,Mi=r.f,Oi=Ne,Pi=Ue.exports,Ti=At,Ii=function(t,e,n){for(var i=yi(e),r=bi.f,u=mi.f,s=0;s<i.length;s++){var a=i[s];ki(t,a)||n&&ki(n,a)||r(t,a,u(e,a))}},ji=Di,Ri=function(t,e){var n,i,r,u,s,a=t.target,o=t.global,l=t.stat;if(n=o?xi:l?xi[a]||Ti(a,{}):(xi[a]||{}).prototype)for(i in e){if(u=e[i],r=t.noTargetGet?(s=Mi(n,i))&&s.value:n[i],!ji(o?i:a+(l?".":"#")+i,t.forced)&&void 0!==r){if(typeof u==typeof r)continue;Ii(u,r)}(t.sham||r&&r.sham)&&Oi(u,"sham",!0),Pi(n,i,u,t)}},Vi=si,Li=ai,Ni=Object.keys||function(t){return Vi(t,Li)},Ui=s,zi=C,qi=h,Hi=u,Yi=Ni,Zi=hi,Gi=c,Ki=Dt,$i=P,Wi=Object.assign,Xi=Object.defineProperty,Ji=zi([].concat),Qi=!Wi||Hi((function(){if(Ui&&1!==Wi({b:1},Wi(Xi({},"a",{enumerable:!0,get:function(){Xi(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},e={},n=Symbol(),i="abcdefghijklmnopqrst";return t[n]=7,i.split("").forEach((function(t){e[t]=t})),7!=Wi({},t)[n]||Yi(Wi({},e)).join("")!=i}))?function(t,e){for(var n=Ki(t),i=arguments.length,r=1,u=Zi.f,s=Gi.f;i>r;)for(var a,o=$i(arguments[r++]),l=u?Ji(Yi(o),u(o)):Yi(o),h=l.length,c=0;h>c;)a=l[c++],Ui&&!qi(s,o,a)||(n[a]=o[a]);return n}:Wi,tr=Qi;Ri({target:"Object",stat:!0,forced:Object.assign!==tr},{assign:tr});var er={};er[Kt("toStringTag")]="z";var nr=i,ir="[object z]"===String(er),rr=L,ur=w,sr=Kt("toStringTag"),ar=nr.Object,or="Arguments"==ur(function(){return arguments}()),lr=ir?ur:function(t){var e,n,i;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=function(t,e){try{return t[e]}catch(t){}}(e=ar(t),sr))?n:or?ur(e):"Object"==(i=ur(e))&&rr(e.callee)?"Arguments":i},hr=i.String,cr=function(t){if("Symbol"===lr(t))throw TypeError("Cannot convert a Symbol value to a string");return hr(t)},fr=zn,pr=cr,dr=I,vr=i.RangeError,gr=function(t){var e=pr(dr(this)),n="",i=fr(t);if(i<0||i==1/0)throw vr("Wrong number of repetitions");for(;i>0;(i>>>=1)&&(e+=e))1&i&&(n+=e);return n};Ri({target:"String",proto:!0},{repeat:gr});var kr=C,yr=Kn,mr=cr,br=I,_r=kr(gr),Ar=kr("".slice),Cr=Math.ceil,Er=function(t){return function(e,n,i){var r,u,s=mr(br(e)),a=yr(n),o=s.length,l=void 0===i?" ":mr(i);return a<=o||""==l?s:((u=_r(l,Cr((r=a-o)/l.length))).length>r&&(u=Ar(u,0,r)),t?s+u:u+s)}},Fr={start:Er(!1),end:Er(!0)},Sr=/Version\/10(?:\.\d+){1,2}(?: [\w./]+)?(?: Mobile\/\w+)? Safari\//.test(G),wr=Fr.start;Ri({target:"String",proto:!0,forced:Sr},{padStart:function(t){return wr(this,t,arguments.length>1?arguments[1]:void 0)}});var Br=Fr.end;function Dr(t){return Dr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Dr(t)}function xr(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Mr(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function Or(t,e,n){return e&&Mr(t.prototype,e),n&&Mr(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function Pr(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Ir(t,e)}function Tr(t){return Tr=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},Tr(t)}function Ir(t,e){return Ir=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},Ir(t,e)}function jr(t,e){if(null==t)return{};var n,i,r=function(t,e){if(null==t)return{};var n,i,r={},u=Object.keys(t);for(i=0;i<u.length;i++)n=u[i],e.indexOf(n)>=0||(r[n]=t[n]);return r}(t,e);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(t);for(i=0;i<u.length;i++)n=u[i],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function Rr(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function Vr(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,i=Tr(t);if(e){var r=Tr(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return Rr(this,n)}}function Lr(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=Tr(t)););return t}function Nr(){return Nr="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){var i=Lr(t,e);if(i){var r=Object.getOwnPropertyDescriptor(i,e);return r.get?r.get.call(arguments.length<3?t:n):r.value}},Nr.apply(this,arguments)}function Ur(t,e,n,i){return Ur="undefined"!=typeof Reflect&&Reflect.set?Reflect.set:function(t,e,n,i){var r,u=Lr(t,e);if(u){if((r=Object.getOwnPropertyDescriptor(u,e)).set)return r.set.call(i,n),!0;if(!r.writable)return!1}if(r=Object.getOwnPropertyDescriptor(i,e)){if(!r.writable)return!1;r.value=n,Object.defineProperty(i,e,r)}else!function(t,e,n){e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n}(i,e,n);return!0},Ur(t,e,n,i)}function zr(t,e,n,i,r){if(!Ur(t,e,n,i||t)&&r)throw new Error("failed to set property");return n}function qr(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null==n)return;var i,r,u=[],s=!0,a=!1;try{for(n=n.call(t);!(s=(i=n.next()).done)&&(u.push(i.value),!e||u.length!==e);s=!0);}catch(t){a=!0,r=t}finally{try{s||null==n.return||n.return()}finally{if(a)throw r}}return u}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return Hr(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Hr(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Hr(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}Ri({target:"String",proto:!0,forced:Sr},{padEnd:function(t){return Br(this,t,arguments.length>1?arguments[1]:void 0)}}),Ri({global:!0},{globalThis:i});var Yr=function(){function t(e){xr(this,t),Object.assign(this,{inserted:"",rawInserted:"",skip:!1,tailShift:0},e)}return Or(t,[{key:"aggregate",value:function(t){return this.rawInserted+=t.rawInserted,this.skip=this.skip||t.skip,this.inserted+=t.inserted,this.tailShift+=t.tailShift,this}},{key:"offset",get:function(){return this.tailShift+this.inserted.length}}]),t}();function Zr(t){return"string"==typeof t||t instanceof String}var Gr="NONE",Kr="LEFT",$r="FORCE_LEFT",Wr="RIGHT",Xr="FORCE_RIGHT";function Jr(t){return t.replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")}function Qr(t){return Array.isArray(t)?t:[t,new Yr]}function tu(t,e){if(e===t)return!0;var n,i=Array.isArray(e),r=Array.isArray(t);if(i&&r){if(e.length!=t.length)return!1;for(n=0;n<e.length;n++)if(!tu(e[n],t[n]))return!1;return!0}if(i!=r)return!1;if(e&&t&&"object"===Dr(e)&&"object"===Dr(t)){var u=e instanceof Date,s=t instanceof Date;if(u&&s)return e.getTime()==t.getTime();if(u!=s)return!1;var a=e instanceof RegExp,o=t instanceof RegExp;if(a&&o)return e.toString()==t.toString();if(a!=o)return!1;var l=Object.keys(e);for(n=0;n<l.length;n++)if(!Object.prototype.hasOwnProperty.call(t,l[n]))return!1;for(n=0;n<l.length;n++)if(!tu(t[l[n]],e[l[n]]))return!1;return!0}return!(!e||!t||"function"!=typeof e||"function"!=typeof t)&&e.toString()===t.toString()}var eu=function(){function t(e,n,i,r){for(xr(this,t),this.value=e,this.cursorPos=n,this.oldValue=i,this.oldSelection=r;this.value.slice(0,this.startChangePos)!==this.oldValue.slice(0,this.startChangePos);)--this.oldSelection.start}return Or(t,[{key:"startChangePos",get:function(){return Math.min(this.cursorPos,this.oldSelection.start)}},{key:"insertedCount",get:function(){return this.cursorPos-this.startChangePos}},{key:"inserted",get:function(){return this.value.substr(this.startChangePos,this.insertedCount)}},{key:"removedCount",get:function(){return Math.max(this.oldSelection.end-this.startChangePos||this.oldValue.length-this.value.length,0)}},{key:"removed",get:function(){return this.oldValue.substr(this.startChangePos,this.removedCount)}},{key:"head",get:function(){return this.value.substring(0,this.startChangePos)}},{key:"tail",get:function(){return this.value.substring(this.startChangePos+this.insertedCount)}},{key:"removeDirection",get:function(){return!this.removedCount||this.insertedCount?Gr:this.oldSelection.end!==this.cursorPos&&this.oldSelection.start!==this.cursorPos||this.oldSelection.end!==this.oldSelection.start?Kr:Wr}}]),t}(),nu=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=arguments.length>2?arguments[2]:void 0;xr(this,t),this.value=e,this.from=n,this.stop=i}return Or(t,[{key:"toString",value:function(){return this.value}},{key:"extend",value:function(t){this.value+=String(t)}},{key:"appendTo",value:function(t){return t.append(this.toString(),{tail:!0}).aggregate(t._appendPlaceholder())}},{key:"state",get:function(){return{value:this.value,from:this.from,stop:this.stop}},set:function(t){Object.assign(this,t)}},{key:"unshift",value:function(t){if(!this.value.length||null!=t&&this.from>=t)return"";var e=this.value[0];return this.value=this.value.slice(1),e}},{key:"shift",value:function(){if(!this.value.length)return"";var t=this.value[this.value.length-1];return this.value=this.value.slice(0,-1),t}}]),t}();function iu(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new iu.InputMask(t,e)}var ru=function(){function t(e){xr(this,t),this._value="",this._update(Object.assign({},t.DEFAULTS,e)),this.isInitialized=!0}return Or(t,[{key:"updateOptions",value:function(t){Object.keys(t).length&&this.withValueRefresh(this._update.bind(this,t))}},{key:"_update",value:function(t){Object.assign(this,t)}},{key:"state",get:function(){return{_value:this.value}},set:function(t){this._value=t._value}},{key:"reset",value:function(){this._value=""}},{key:"value",get:function(){return this._value},set:function(t){this.resolve(t)}},{key:"resolve",value:function(t){return this.reset(),this.append(t,{input:!0},""),this.doCommit(),this.value}},{key:"unmaskedValue",get:function(){return this.value},set:function(t){this.reset(),this.append(t,{},""),this.doCommit()}},{key:"typedValue",get:function(){return this.doParse(this.value)},set:function(t){this.value=this.doFormat(t)}},{key:"rawInputValue",get:function(){return this.extractInput(0,this.value.length,{raw:!0})},set:function(t){this.reset(),this.append(t,{raw:!0},""),this.doCommit()}},{key:"isComplete",get:function(){return!0}},{key:"isFilled",get:function(){return this.isComplete}},{key:"nearestInputPos",value:function(t,e){return t}},{key:"extractInput",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.value.length;return this.value.slice(t,e)}},{key:"extractTail",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.value.length;return new nu(this.extractInput(t,e),t)}},{key:"appendTail",value:function(t){return Zr(t)&&(t=new nu(String(t))),t.appendTo(this)}},{key:"_appendCharRaw",value:function(t){return t?(this._value+=t,new Yr({inserted:t,rawInserted:t})):new Yr}},{key:"_appendChar",value:function(t){var e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2?arguments[2]:void 0,r=this.state,u=Qr(this.doPrepare(t,n)),s=qr(u,2);if(t=s[0],(e=(e=s[1]).aggregate(this._appendCharRaw(t,n))).inserted){var a,o=!1!==this.doValidate(n);if(o&&null!=i){var l=this.state;!0===this.overwrite&&(a=i.state,i.unshift(this.value.length));var h=this.appendTail(i);(o=h.rawInserted===i.toString())&&h.inserted||"shift"!==this.overwrite||(this.state=l,a=i.state,i.shift(),o=(h=this.appendTail(i)).rawInserted===i.toString()),o&&h.inserted&&(this.state=l)}o||(e=new Yr,this.state=r,i&&a&&(i.state=a))}return e}},{key:"_appendPlaceholder",value:function(){return new Yr}},{key:"_appendEager",value:function(){return new Yr}},{key:"append",value:function(t,e,n){if(!Zr(t))throw new Error("value should be string");var i=new Yr,r=Zr(n)?new nu(String(n)):n;e&&e.tail&&(e._beforeTailState=this.state);for(var u=0;u<t.length;++u)i.aggregate(this._appendChar(t[u],e,r));return null!=r&&(i.tailShift+=this.appendTail(r).tailShift),this.eager&&null!=e&&e.input&&t&&i.aggregate(this._appendEager()),i}},{key:"remove",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.value.length;return this._value=this.value.slice(0,t)+this.value.slice(e),new Yr}},{key:"withValueRefresh",value:function(t){if(this._refreshing||!this.isInitialized)return t();this._refreshing=!0;var e=this.rawInputValue,n=this.value,i=t();return this.rawInputValue=e,this.value&&this.value!==n&&0===n.indexOf(this.value)&&this.append(n.slice(this.value.length),{},""),delete this._refreshing,i}},{key:"runIsolated",value:function(t){if(this._isolated||!this.isInitialized)return t(this);this._isolated=!0;var e=this.state,n=t(this);return this.state=e,delete this._isolated,n}},{key:"doPrepare",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.prepare?this.prepare(t,this,e):t}},{key:"doValidate",value:function(t){return(!this.validate||this.validate(this.value,this,t))&&(!this.parent||this.parent.doValidate(t))}},{key:"doCommit",value:function(){this.commit&&this.commit(this.value,this)}},{key:"doFormat",value:function(t){return this.format?this.format(t,this):t}},{key:"doParse",value:function(t){return this.parse?this.parse(t,this):t}},{key:"splice",value:function(t,e,n,i){var r,u=t+e,s=this.extractTail(u);this.eager&&(i=function(t){switch(t){case Kr:return $r;case Wr:return Xr;default:return t}}(i),r=this.extractInput(0,u,{raw:!0}));var a=this.nearestInputPos(t,e>1&&0!==t&&!this.eager?Gr:i),o=new Yr({tailShift:a-t}).aggregate(this.remove(a));if(this.eager&&i!==Gr&&r===this.rawInputValue)if(i===$r)for(var l;r===this.rawInputValue&&(l=this.value.length);)o.aggregate(new Yr({tailShift:-1})).aggregate(this.remove(l-1));else i===Xr&&s.unshift();return o.aggregate(this.append(n,{input:!0},s))}},{key:"maskEquals",value:function(t){return this.mask===t}}]),t}();function uu(t){if(null==t)throw new Error("mask property should be defined");return t instanceof RegExp?iu.MaskedRegExp:Zr(t)?iu.MaskedPattern:t instanceof Date||t===Date?iu.MaskedDate:t instanceof Number||"number"==typeof t||t===Number?iu.MaskedNumber:Array.isArray(t)||t===Array?iu.MaskedDynamic:iu.Masked&&t.prototype instanceof iu.Masked?t:t instanceof iu.Masked?t.constructor:t instanceof Function?iu.MaskedFunction:(console.warn("Mask not found for mask",t),iu.Masked)}function su(t){if(iu.Masked&&t instanceof iu.Masked)return t;var e=(t=Object.assign({},t)).mask;if(iu.Masked&&e instanceof iu.Masked)return e;var n=uu(e);if(!n)throw new Error("Masked class is not found for provided mask, appropriate module needs to be import manually before creating mask.");return new n(t)}ru.DEFAULTS={format:function(t){return t},parse:function(t){return t}},iu.Masked=ru,iu.createMask=su;var au=["mask"],ou={0:/\d/,a:/[\u0041-\u005A\u0061-\u007A\u00AA\u00B5\u00BA\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u0527\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0\u08A2-\u08AC\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0977\u0979-\u097F\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C33\u0C35-\u0C39\u0C3D\u0C58\u0C59\u0C60\u0C61\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D60\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F4\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191C\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19C1-\u19C7\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FCC\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA697\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788\uA78B-\uA78E\uA790-\uA793\uA7A0-\uA7AA\uA7F8-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA80-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uABC0-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]/,"*":/./},lu=function(){function t(e){xr(this,t);var n=e.mask,i=jr(e,au);this.masked=su({mask:n}),Object.assign(this,i)}return Or(t,[{key:"reset",value:function(){this.isFilled=!1,this.masked.reset()}},{key:"remove",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.value.length;return 0===t&&e>=1?(this.isFilled=!1,this.masked.remove(t,e)):new Yr}},{key:"value",get:function(){return this.masked.value||(this.isFilled&&!this.isOptional?this.placeholderChar:"")}},{key:"unmaskedValue",get:function(){return this.masked.unmaskedValue}},{key:"isComplete",get:function(){return Boolean(this.masked.value)||this.isOptional}},{key:"_appendChar",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this.isFilled)return new Yr;var n=this.masked.state,i=this.masked._appendChar(t,e);return i.inserted&&!1===this.doValidate(e)&&(i.inserted=i.rawInserted="",this.masked.state=n),i.inserted||this.isOptional||this.lazy||e.input||(i.inserted=this.placeholderChar),i.skip=!i.inserted&&!this.isOptional,this.isFilled=Boolean(i.inserted),i}},{key:"append",value:function(){var t;return(t=this.masked).append.apply(t,arguments)}},{key:"_appendPlaceholder",value:function(){var t=new Yr;return this.isFilled||this.isOptional||(this.isFilled=!0,t.inserted=this.placeholderChar),t}},{key:"_appendEager",value:function(){return new Yr}},{key:"extractTail",value:function(){var t;return(t=this.masked).extractTail.apply(t,arguments)}},{key:"appendTail",value:function(){var t;return(t=this.masked).appendTail.apply(t,arguments)}},{key:"extractInput",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.value.length,n=arguments.length>2?arguments[2]:void 0;return this.masked.extractInput(t,e,n)}},{key:"nearestInputPos",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Gr,n=0,i=this.value.length,r=Math.min(Math.max(t,n),i);switch(e){case Kr:case $r:return this.isComplete?r:n;case Wr:case Xr:return this.isComplete?r:i;default:return r}}},{key:"doValidate",value:function(){var t,e;return(t=this.masked).doValidate.apply(t,arguments)&&(!this.parent||(e=this.parent).doValidate.apply(e,arguments))}},{key:"doCommit",value:function(){this.masked.doCommit()}},{key:"state",get:function(){return{masked:this.masked.state,isFilled:this.isFilled}},set:function(t){this.masked.state=t.masked,this.isFilled=t.isFilled}}]),t}(),hu=function(){function t(e){xr(this,t),Object.assign(this,e),this._value="",this.isFixed=!0}return Or(t,[{key:"value",get:function(){return this._value}},{key:"unmaskedValue",get:function(){return this.isUnmasking?this.value:""}},{key:"reset",value:function(){this._isRawInput=!1,this._value=""}},{key:"remove",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this._value.length;return this._value=this._value.slice(0,t)+this._value.slice(e),this._value||(this._isRawInput=!1),new Yr}},{key:"nearestInputPos",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Gr,n=0,i=this._value.length;switch(e){case Kr:case $r:return n;default:return i}}},{key:"extractInput",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this._value.length,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return n.raw&&this._isRawInput&&this._value.slice(t,e)||""}},{key:"isComplete",get:function(){return!0}},{key:"isFilled",get:function(){return Boolean(this._value)}},{key:"_appendChar",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=new Yr;if(this._value)return n;var i=this.char===t,r=i&&(this.isUnmasking||e.input||e.raw)&&!this.eager&&!e.tail;return r&&(n.rawInserted=this.char),this._value=n.inserted=this.char,this._isRawInput=r&&(e.raw||e.input),n}},{key:"_appendEager",value:function(){return this._appendChar(this.char)}},{key:"_appendPlaceholder",value:function(){var t=new Yr;return this._value||(this._value=t.inserted=this.char),t}},{key:"extractTail",value:function(){return arguments.length>1&&void 0!==arguments[1]||this.value.length,new nu("")}},{key:"appendTail",value:function(t){return Zr(t)&&(t=new nu(String(t))),t.appendTo(this)}},{key:"append",value:function(t,e,n){var i=this._appendChar(t[0],e);return null!=n&&(i.tailShift+=this.appendTail(n).tailShift),i}},{key:"doCommit",value:function(){}},{key:"state",get:function(){return{_value:this._value,_isRawInput:this._isRawInput}},set:function(t){Object.assign(this,t)}}]),t}(),cu=["chunks"],fu=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;xr(this,t),this.chunks=e,this.from=n}return Or(t,[{key:"toString",value:function(){return this.chunks.map(String).join("")}},{key:"extend",value:function(e){if(String(e)){Zr(e)&&(e=new nu(String(e)));var n=this.chunks[this.chunks.length-1],i=n&&(n.stop===e.stop||null==e.stop)&&e.from===n.from+n.toString().length;if(e instanceof nu)i?n.extend(e.toString()):this.chunks.push(e);else if(e instanceof t){if(null==e.stop)for(var r;e.chunks.length&&null==e.chunks[0].stop;)(r=e.chunks.shift()).from+=e.from,this.extend(r);e.toString()&&(e.stop=e.blockIndex,this.chunks.push(e))}}}},{key:"appendTo",value:function(e){if(!(e instanceof iu.MaskedPattern))return new nu(this.toString()).appendTo(e);for(var n=new Yr,i=0;i<this.chunks.length&&!n.skip;++i){var r=this.chunks[i],u=e._mapPosToBlock(e.value.length),s=r.stop,a=void 0;if(null!=s&&(!u||u.index<=s)&&((r instanceof t||e._stops.indexOf(s)>=0)&&n.aggregate(e._appendPlaceholder(s)),a=r instanceof t&&e._blocks[s]),a){var o=a.appendTail(r);o.skip=!1,n.aggregate(o),e._value+=o.inserted;var l=r.toString().slice(o.rawInserted.length);l&&n.aggregate(e.append(l,{tail:!0}))}else n.aggregate(e.append(r.toString(),{tail:!0}))}return n}},{key:"state",get:function(){return{chunks:this.chunks.map((function(t){return t.state})),from:this.from,stop:this.stop,blockIndex:this.blockIndex}},set:function(e){var n=e.chunks,i=jr(e,cu);Object.assign(this,i),this.chunks=n.map((function(e){var n="chunks"in e?new t:new nu;return n.state=e,n}))}},{key:"unshift",value:function(t){if(!this.chunks.length||null!=t&&this.from>=t)return"";for(var e=null!=t?t-this.from:t,n=0;n<this.chunks.length;){var i=this.chunks[n],r=i.unshift(e);if(i.toString()){if(!r)break;++n}else this.chunks.splice(n,1);if(r)return r}return""}},{key:"shift",value:function(){if(!this.chunks.length)return"";for(var t=this.chunks.length-1;0<=t;){var e=this.chunks[t],n=e.shift();if(e.toString()){if(!n)break;--t}else this.chunks.splice(t,1);if(n)return n}return""}}]),t}(),pu=function(){function t(e,n){xr(this,t),this.masked=e,this._log=[];var i=e._mapPosToBlock(n)||(n<0?{index:0,offset:0}:{index:this.masked._blocks.length,offset:0}),r=i.offset,u=i.index;this.offset=r,this.index=u,this.ok=!1}return Or(t,[{key:"block",get:function(){return this.masked._blocks[this.index]}},{key:"pos",get:function(){return this.masked._blockStartPos(this.index)+this.offset}},{key:"state",get:function(){return{index:this.index,offset:this.offset,ok:this.ok}},set:function(t){Object.assign(this,t)}},{key:"pushState",value:function(){this._log.push(this.state)}},{key:"popState",value:function(){var t=this._log.pop();return this.state=t,t}},{key:"bindBlock",value:function(){this.block||(this.index<0&&(this.index=0,this.offset=0),this.index>=this.masked._blocks.length&&(this.index=this.masked._blocks.length-1,this.offset=this.block.value.length))}},{key:"_pushLeft",value:function(t){for(this.pushState(),this.bindBlock();0<=this.index;--this.index,this.offset=(null===(e=this.block)||void 0===e?void 0:e.value.length)||0){var e;if(t())return this.ok=!0}return this.ok=!1}},{key:"_pushRight",value:function(t){for(this.pushState(),this.bindBlock();this.index<this.masked._blocks.length;++this.index,this.offset=0)if(t())return this.ok=!0;return this.ok=!1}},{key:"pushLeftBeforeFilled",value:function(){var t=this;return this._pushLeft((function(){if(!t.block.isFixed&&t.block.value)return t.offset=t.block.nearestInputPos(t.offset,$r),0!==t.offset||void 0}))}},{key:"pushLeftBeforeInput",value:function(){var t=this;return this._pushLeft((function(){if(!t.block.isFixed)return t.offset=t.block.nearestInputPos(t.offset,Kr),!0}))}},{key:"pushLeftBeforeRequired",value:function(){var t=this;return this._pushLeft((function(){if(!(t.block.isFixed||t.block.isOptional&&!t.block.value))return t.offset=t.block.nearestInputPos(t.offset,Kr),!0}))}},{key:"pushRightBeforeFilled",value:function(){var t=this;return this._pushRight((function(){if(!t.block.isFixed&&t.block.value)return t.offset=t.block.nearestInputPos(t.offset,Xr),t.offset!==t.block.value.length||void 0}))}},{key:"pushRightBeforeInput",value:function(){var t=this;return this._pushRight((function(){if(!t.block.isFixed)return t.offset=t.block.nearestInputPos(t.offset,Gr),!0}))}},{key:"pushRightBeforeRequired",value:function(){var t=this;return this._pushRight((function(){if(!(t.block.isFixed||t.block.isOptional&&!t.block.value))return t.offset=t.block.nearestInputPos(t.offset,Gr),!0}))}}]),t}(),du=function(t){Pr(n,t);var e=Vr(n);function n(){return xr(this,n),e.apply(this,arguments)}return Or(n,[{key:"_update",value:function(t){t.mask&&(t.validate=function(e){return e.search(t.mask)>=0}),Nr(Tr(n.prototype),"_update",this).call(this,t)}}]),n}(ru);iu.MaskedRegExp=du;var vu=["_blocks"],gu=function(t){Pr(n,t);var e=Vr(n);function n(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return xr(this,n),t.definitions=Object.assign({},ou,t.definitions),e.call(this,Object.assign({},n.DEFAULTS,t))}return Or(n,[{key:"_update",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};t.definitions=Object.assign({},this.definitions,t.definitions),Nr(Tr(n.prototype),"_update",this).call(this,t),this._rebuildMask()}},{key:"_rebuildMask",value:function(){var t=this,e=this.definitions;this._blocks=[],this._stops=[],this._maskedBlocks={};var i=this.mask;if(i&&e)for(var r=!1,u=!1,s=0;s<i.length;++s){if(this.blocks)if("continue"===function(){var e=i.slice(s),n=Object.keys(t.blocks).filter((function(t){return 0===e.indexOf(t)}));n.sort((function(t,e){return e.length-t.length}));var r=n[0];if(r){var u=su(Object.assign({parent:t,lazy:t.lazy,eager:t.eager,placeholderChar:t.placeholderChar,overwrite:t.overwrite},t.blocks[r]));return u&&(t._blocks.push(u),t._maskedBlocks[r]||(t._maskedBlocks[r]=[]),t._maskedBlocks[r].push(t._blocks.length-1)),s+=r.length-1,"continue"}}())continue;var a=i[s],o=a in e;if(a!==n.STOP_CHAR)if("{"!==a&&"}"!==a)if("["!==a&&"]"!==a){if(a===n.ESCAPE_CHAR){if(++s,!(a=i[s]))break;o=!1}var l=o?new lu({parent:this,lazy:this.lazy,eager:this.eager,placeholderChar:this.placeholderChar,mask:e[a],isOptional:u}):new hu({char:a,eager:this.eager,isUnmasking:r});this._blocks.push(l)}else u=!u;else r=!r;else this._stops.push(this._blocks.length)}}},{key:"state",get:function(){return Object.assign({},Nr(Tr(n.prototype),"state",this),{_blocks:this._blocks.map((function(t){return t.state}))})},set:function(t){var e=t._blocks,i=jr(t,vu);this._blocks.forEach((function(t,n){return t.state=e[n]})),zr(Tr(n.prototype),"state",i,this,!0)}},{key:"reset",value:function(){Nr(Tr(n.prototype),"reset",this).call(this),this._blocks.forEach((function(t){return t.reset()}))}},{key:"isComplete",get:function(){return this._blocks.every((function(t){return t.isComplete}))}},{key:"isFilled",get:function(){return this._blocks.every((function(t){return t.isFilled}))}},{key:"isFixed",get:function(){return this._blocks.every((function(t){return t.isFixed}))}},{key:"isOptional",get:function(){return this._blocks.every((function(t){return t.isOptional}))}},{key:"doCommit",value:function(){this._blocks.forEach((function(t){return t.doCommit()})),Nr(Tr(n.prototype),"doCommit",this).call(this)}},{key:"unmaskedValue",get:function(){return this._blocks.reduce((function(t,e){return t+e.unmaskedValue}),"")},set:function(t){zr(Tr(n.prototype),"unmaskedValue",t,this,!0)}},{key:"value",get:function(){return this._blocks.reduce((function(t,e){return t+e.value}),"")},set:function(t){zr(Tr(n.prototype),"value",t,this,!0)}},{key:"appendTail",value:function(t){return Nr(Tr(n.prototype),"appendTail",this).call(this,t).aggregate(this._appendPlaceholder())}},{key:"_appendEager",value:function(){var t,e=new Yr,n=null===(t=this._mapPosToBlock(this.value.length))||void 0===t?void 0:t.index;if(null==n)return e;this._blocks[n].isFilled&&++n;for(var i=n;i<this._blocks.length;++i){var r=this._blocks[i]._appendEager();if(!r.inserted)break;e.aggregate(r)}return e}},{key:"_appendCharRaw",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=this._mapPosToBlock(this.value.length),i=new Yr;if(!n)return i;for(var r=n.index;;++r){var u,s=this._blocks[r];if(!s)break;var a=s._appendChar(t,Object.assign({},e,{_beforeTailState:null===(u=e._beforeTailState)||void 0===u?void 0:u._blocks[r]})),o=a.skip;if(i.aggregate(a),o||a.rawInserted)break}return i}},{key:"extractTail",value:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.value.length,i=new fu;return e===n||this._forEachBlocksInRange(e,n,(function(e,n,r,u){var s=e.extractTail(r,u);s.stop=t._findStopBefore(n),s.from=t._blockStartPos(n),s instanceof fu&&(s.blockIndex=n),i.extend(s)})),i}},{key:"extractInput",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.value.length,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(t===e)return"";var i="";return this._forEachBlocksInRange(t,e,(function(t,e,r,u){i+=t.extractInput(r,u,n)})),i}},{key:"_findStopBefore",value:function(t){for(var e,n=0;n<this._stops.length;++n){var i=this._stops[n];if(!(i<=t))break;e=i}return e}},{key:"_appendPlaceholder",value:function(t){var e=this,n=new Yr;if(this.lazy&&null==t)return n;var i=this._mapPosToBlock(this.value.length);if(!i)return n;var r=i.index,u=null!=t?t:this._blocks.length;return this._blocks.slice(r,u).forEach((function(i){if(!i.lazy||null!=t){var r=null!=i._blocks?[i._blocks.length]:[],u=i._appendPlaceholder.apply(i,r);e._value+=u.inserted,n.aggregate(u)}})),n}},{key:"_mapPosToBlock",value:function(t){for(var e="",n=0;n<this._blocks.length;++n){var i=this._blocks[n],r=e.length;if(t<=(e+=i.value).length)return{index:n,offset:t-r}}}},{key:"_blockStartPos",value:function(t){return this._blocks.slice(0,t).reduce((function(t,e){return t+e.value.length}),0)}},{key:"_forEachBlocksInRange",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.value.length,n=arguments.length>2?arguments[2]:void 0,i=this._mapPosToBlock(t);if(i){var r=this._mapPosToBlock(e),u=r&&i.index===r.index,s=i.offset,a=r&&u?r.offset:this._blocks[i.index].value.length;if(n(this._blocks[i.index],i.index,s,a),r&&!u){for(var o=i.index+1;o<r.index;++o)n(this._blocks[o],o,0,this._blocks[o].value.length);n(this._blocks[r.index],r.index,0,r.offset)}}}},{key:"remove",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.value.length,i=Nr(Tr(n.prototype),"remove",this).call(this,t,e);return this._forEachBlocksInRange(t,e,(function(t,e,n,r){i.aggregate(t.remove(n,r))})),i}},{key:"nearestInputPos",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Gr;if(!this._blocks.length)return 0;var n=new pu(this,t);if(e===Gr)return n.pushRightBeforeInput()?n.pos:(n.popState(),n.pushLeftBeforeInput()?n.pos:this.value.length);if(e===Kr||e===$r){if(e===Kr){if(n.pushRightBeforeFilled(),n.ok&&n.pos===t)return t;n.popState()}if(n.pushLeftBeforeInput(),n.pushLeftBeforeRequired(),n.pushLeftBeforeFilled(),e===Kr){if(n.pushRightBeforeInput(),n.pushRightBeforeRequired(),n.ok&&n.pos<=t)return n.pos;if(n.popState(),n.ok&&n.pos<=t)return n.pos;n.popState()}return n.ok?n.pos:e===$r?0:(n.popState(),n.ok?n.pos:(n.popState(),n.ok?n.pos:0))}return e===Wr||e===Xr?(n.pushRightBeforeInput(),n.pushRightBeforeRequired(),n.pushRightBeforeFilled()?n.pos:e===Xr?this.value.length:(n.popState(),n.ok?n.pos:(n.popState(),n.ok?n.pos:this.nearestInputPos(t,Kr)))):t}},{key:"maskedBlock",value:function(t){return this.maskedBlocks(t)[0]}},{key:"maskedBlocks",value:function(t){var e=this,n=this._maskedBlocks[t];return n?n.map((function(t){return e._blocks[t]})):[]}}]),n}(ru);gu.DEFAULTS={lazy:!0,placeholderChar:"_"},gu.STOP_CHAR="`",gu.ESCAPE_CHAR="\\",gu.InputDefinition=lu,gu.FixedDefinition=hu,iu.MaskedPattern=gu;var ku=function(t){Pr(n,t);var e=Vr(n);function n(){return xr(this,n),e.apply(this,arguments)}return Or(n,[{key:"_matchFrom",get:function(){return this.maxLength-String(this.from).length}},{key:"_update",value:function(t){t=Object.assign({to:this.to||0,from:this.from||0,maxLength:this.maxLength||0},t);var e=String(t.to).length;null!=t.maxLength&&(e=Math.max(e,t.maxLength)),t.maxLength=e;for(var i=String(t.from).padStart(e,"0"),r=String(t.to).padStart(e,"0"),u=0;u<r.length&&r[u]===i[u];)++u;t.mask=r.slice(0,u).replace(/0/g,"\\0")+"0".repeat(e-u),Nr(Tr(n.prototype),"_update",this).call(this,t)}},{key:"isComplete",get:function(){return Nr(Tr(n.prototype),"isComplete",this)&&Boolean(this.value)}},{key:"boundaries",value:function(t){var e="",n="",i=qr(t.match(/^(\D*)(\d*)(\D*)/)||[],3),r=i[1],u=i[2];return u&&(e="0".repeat(r.length)+u,n="9".repeat(r.length)+u),[e=e.padEnd(this.maxLength,"0"),n=n.padEnd(this.maxLength,"9")]}},{key:"doPrepare",value:function(t){var e,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=Qr(Nr(Tr(n.prototype),"doPrepare",this).call(this,t.replace(/\D/g,""),i)),u=qr(r,2);if(t=u[0],e=u[1],!this.autofix||!t)return t;var s=String(this.from).padStart(this.maxLength,"0"),a=String(this.to).padStart(this.maxLength,"0"),o=this.value+t;if(o.length>this.maxLength)return"";var l=this.boundaries(o),h=qr(l,2),c=h[0],f=h[1];return Number(f)<this.from?s[o.length-1]:Number(c)>this.to?"pad"===this.autofix&&o.length<this.maxLength?["",e.aggregate(this.append(s[o.length-1]+t,i))]:a[o.length-1]:t}},{key:"doValidate",value:function(){var t,e=this.value,i=e.search(/[^0]/);if(-1===i&&e.length<=this._matchFrom)return!0;for(var r=this.boundaries(e),u=qr(r,2),s=u[0],a=u[1],o=arguments.length,l=new Array(o),h=0;h<o;h++)l[h]=arguments[h];return this.from<=Number(a)&&Number(s)<=this.to&&(t=Nr(Tr(n.prototype),"doValidate",this)).call.apply(t,[this].concat(l))}}]),n}(gu);iu.MaskedRange=ku;var yu=function(t){Pr(n,t);var e=Vr(n);function n(t){return xr(this,n),e.call(this,Object.assign({},n.DEFAULTS,t))}return Or(n,[{key:"_update",value:function(t){t.mask===Date&&delete t.mask,t.pattern&&(t.mask=t.pattern);var e=t.blocks;t.blocks=Object.assign({},n.GET_DEFAULT_BLOCKS()),t.min&&(t.blocks.Y.from=t.min.getFullYear()),t.max&&(t.blocks.Y.to=t.max.getFullYear()),t.min&&t.max&&t.blocks.Y.from===t.blocks.Y.to&&(t.blocks.m.from=t.min.getMonth()+1,t.blocks.m.to=t.max.getMonth()+1,t.blocks.m.from===t.blocks.m.to&&(t.blocks.d.from=t.min.getDate(),t.blocks.d.to=t.max.getDate())),Object.assign(t.blocks,this.blocks,e),Object.keys(t.blocks).forEach((function(e){var n=t.blocks[e];!("autofix"in n)&&"autofix"in t&&(n.autofix=t.autofix)})),Nr(Tr(n.prototype),"_update",this).call(this,t)}},{key:"doValidate",value:function(){for(var t,e=this.date,i=arguments.length,r=new Array(i),u=0;u<i;u++)r[u]=arguments[u];return(t=Nr(Tr(n.prototype),"doValidate",this)).call.apply(t,[this].concat(r))&&(!this.isComplete||this.isDateExist(this.value)&&null!=e&&(null==this.min||this.min<=e)&&(null==this.max||e<=this.max))}},{key:"isDateExist",value:function(t){return this.format(this.parse(t,this),this).indexOf(t)>=0}},{key:"date",get:function(){return this.typedValue},set:function(t){this.typedValue=t}},{key:"typedValue",get:function(){return this.isComplete?Nr(Tr(n.prototype),"typedValue",this):null},set:function(t){zr(Tr(n.prototype),"typedValue",t,this,!0)}},{key:"maskEquals",value:function(t){return t===Date||Nr(Tr(n.prototype),"maskEquals",this).call(this,t)}}]),n}(gu);yu.DEFAULTS={pattern:"d{.}`m{.}`Y",format:function(t){return t?[String(t.getDate()).padStart(2,"0"),String(t.getMonth()+1).padStart(2,"0"),t.getFullYear()].join("."):""},parse:function(t){var e=qr(t.split("."),3),n=e[0],i=e[1],r=e[2];return new Date(r,i-1,n)}},yu.GET_DEFAULT_BLOCKS=function(){return{d:{mask:ku,from:1,to:31,maxLength:2},m:{mask:ku,from:1,to:12,maxLength:2},Y:{mask:ku,from:1900,to:9999}}},iu.MaskedDate=yu;var mu=function(){function t(){xr(this,t)}return Or(t,[{key:"selectionStart",get:function(){var t;try{t=this._unsafeSelectionStart}catch(t){}return null!=t?t:this.value.length}},{key:"selectionEnd",get:function(){var t;try{t=this._unsafeSelectionEnd}catch(t){}return null!=t?t:this.value.length}},{key:"select",value:function(t,e){if(null!=t&&null!=e&&(t!==this.selectionStart||e!==this.selectionEnd))try{this._unsafeSelect(t,e)}catch(t){}}},{key:"_unsafeSelect",value:function(t,e){}},{key:"isActive",get:function(){return!1}},{key:"bindEvents",value:function(t){}},{key:"unbindEvents",value:function(){}}]),t}();iu.MaskElement=mu;var bu=function(t){Pr(n,t);var e=Vr(n);function n(t){var i;return xr(this,n),(i=e.call(this)).input=t,i._handlers={},i}return Or(n,[{key:"rootElement",get:function(){var t,e,n;return null!==(t=null===(e=(n=this.input).getRootNode)||void 0===e?void 0:e.call(n))&&void 0!==t?t:document}},{key:"isActive",get:function(){return this.input===this.rootElement.activeElement}},{key:"_unsafeSelectionStart",get:function(){return this.input.selectionStart}},{key:"_unsafeSelectionEnd",get:function(){return this.input.selectionEnd}},{key:"_unsafeSelect",value:function(t,e){this.input.setSelectionRange(t,e)}},{key:"value",get:function(){return this.input.value},set:function(t){this.input.value=t}},{key:"bindEvents",value:function(t){var e=this;Object.keys(t).forEach((function(i){return e._toggleEventHandler(n.EVENTS_MAP[i],t[i])}))}},{key:"unbindEvents",value:function(){var t=this;Object.keys(this._handlers).forEach((function(e){return t._toggleEventHandler(e)}))}},{key:"_toggleEventHandler",value:function(t,e){this._handlers[t]&&(this.input.removeEventListener(t,this._handlers[t]),delete this._handlers[t]),e&&(this.input.addEventListener(t,e),this._handlers[t]=e)}}]),n}(mu);bu.EVENTS_MAP={selectionChange:"keydown",input:"input",drop:"drop",click:"click",focus:"focus",commit:"blur"},iu.HTMLMaskElement=bu;var _u=function(t){Pr(n,t);var e=Vr(n);function n(){return xr(this,n),e.apply(this,arguments)}return Or(n,[{key:"_unsafeSelectionStart",get:function(){var t=this.rootElement,e=t.getSelection&&t.getSelection(),n=e&&e.anchorOffset,i=e&&e.focusOffset;return null==i||null==n||n<i?n:i}},{key:"_unsafeSelectionEnd",get:function(){var t=this.rootElement,e=t.getSelection&&t.getSelection(),n=e&&e.anchorOffset,i=e&&e.focusOffset;return null==i||null==n||n>i?n:i}},{key:"_unsafeSelect",value:function(t,e){if(this.rootElement.createRange){var n=this.rootElement.createRange();n.setStart(this.input.firstChild||this.input,t),n.setEnd(this.input.lastChild||this.input,e);var i=this.rootElement,r=i.getSelection&&i.getSelection();r&&(r.removeAllRanges(),r.addRange(n))}}},{key:"value",get:function(){return this.input.textContent},set:function(t){this.input.textContent=t}}]),n}(bu);iu.HTMLContenteditableMaskElement=_u;var Au=["mask"],Cu=function(){function t(e,n){xr(this,t),this.el=e instanceof mu?e:e.isContentEditable&&"INPUT"!==e.tagName&&"TEXTAREA"!==e.tagName?new _u(e):new bu(e),this.masked=su(n),this._listeners={},this._value="",this._unmaskedValue="",this._saveSelection=this._saveSelection.bind(this),this._onInput=this._onInput.bind(this),this._onChange=this._onChange.bind(this),this._onDrop=this._onDrop.bind(this),this._onFocus=this._onFocus.bind(this),this._onClick=this._onClick.bind(this),this.alignCursor=this.alignCursor.bind(this),this.alignCursorFriendly=this.alignCursorFriendly.bind(this),this._bindEvents(),this.updateValue(),this._onChange()}return Or(t,[{key:"mask",get:function(){return this.masked.mask},set:function(t){if(!this.maskEquals(t))if(t instanceof iu.Masked||this.masked.constructor!==uu(t)){var e=su({mask:t});e.unmaskedValue=this.masked.unmaskedValue,this.masked=e}else this.masked.updateOptions({mask:t})}},{key:"maskEquals",value:function(t){var e;return null==t||(null===(e=this.masked)||void 0===e?void 0:e.maskEquals(t))}},{key:"value",get:function(){return this._value},set:function(t){this.masked.value=t,this.updateControl(),this.alignCursor()}},{key:"unmaskedValue",get:function(){return this._unmaskedValue},set:function(t){this.masked.unmaskedValue=t,this.updateControl(),this.alignCursor()}},{key:"typedValue",get:function(){return this.masked.typedValue},set:function(t){this.masked.typedValue=t,this.updateControl(),this.alignCursor()}},{key:"_bindEvents",value:function(){this.el.bindEvents({selectionChange:this._saveSelection,input:this._onInput,drop:this._onDrop,click:this._onClick,focus:this._onFocus,commit:this._onChange})}},{key:"_unbindEvents",value:function(){this.el&&this.el.unbindEvents()}},{key:"_fireEvent",value:function(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),i=1;i<e;i++)n[i-1]=arguments[i];var r=this._listeners[t];r&&r.forEach((function(t){return t.apply(void 0,n)}))}},{key:"selectionStart",get:function(){return this._cursorChanging?this._changingCursorPos:this.el.selectionStart}},{key:"cursorPos",get:function(){return this._cursorChanging?this._changingCursorPos:this.el.selectionEnd},set:function(t){this.el&&this.el.isActive&&(this.el.select(t,t),this._saveSelection())}},{key:"_saveSelection",value:function(){this.value!==this.el.value&&console.warn("Element value was changed outside of mask. Syncronize mask using `mask.updateValue()` to work properly."),this._selection={start:this.selectionStart,end:this.cursorPos}}},{key:"updateValue",value:function(){this.masked.value=this.el.value,this._value=this.masked.value}},{key:"updateControl",value:function(){var t=this.masked.unmaskedValue,e=this.masked.value,n=this.unmaskedValue!==t||this.value!==e;this._unmaskedValue=t,this._value=e,this.el.value!==e&&(this.el.value=e),n&&this._fireChangeEvents()}},{key:"updateOptions",value:function(t){var e=t.mask,n=jr(t,Au),i=!this.maskEquals(e),r=!tu(this.masked,n);i&&(this.mask=e),r&&this.masked.updateOptions(n),(i||r)&&this.updateControl()}},{key:"updateCursor",value:function(t){null!=t&&(this.cursorPos=t,this._delayUpdateCursor(t))}},{key:"_delayUpdateCursor",value:function(t){var e=this;this._abortUpdateCursor(),this._changingCursorPos=t,this._cursorChanging=setTimeout((function(){e.el&&(e.cursorPos=e._changingCursorPos,e._abortUpdateCursor())}),10)}},{key:"_fireChangeEvents",value:function(){this._fireEvent("accept",this._inputEvent),this.masked.isComplete&&this._fireEvent("complete",this._inputEvent)}},{key:"_abortUpdateCursor",value:function(){this._cursorChanging&&(clearTimeout(this._cursorChanging),delete this._cursorChanging)}},{key:"alignCursor",value:function(){this.cursorPos=this.masked.nearestInputPos(this.masked.nearestInputPos(this.cursorPos,Kr))}},{key:"alignCursorFriendly",value:function(){this.selectionStart===this.cursorPos&&this.alignCursor()}},{key:"on",value:function(t,e){return this._listeners[t]||(this._listeners[t]=[]),this._listeners[t].push(e),this}},{key:"off",value:function(t,e){if(!this._listeners[t])return this;if(!e)return delete this._listeners[t],this;var n=this._listeners[t].indexOf(e);return n>=0&&this._listeners[t].splice(n,1),this}},{key:"_onInput",value:function(t){if(this._inputEvent=t,this._abortUpdateCursor(),!this._selection)return this.updateValue();var e=new eu(this.el.value,this.cursorPos,this.value,this._selection),n=this.masked.rawInputValue,i=this.masked.splice(e.startChangePos,e.removed.length,e.inserted,e.removeDirection).offset,r=n===this.masked.rawInputValue?e.removeDirection:Gr,u=this.masked.nearestInputPos(e.startChangePos+i,r);r!==Gr&&(u=this.masked.nearestInputPos(u,Gr)),this.updateControl(),this.updateCursor(u),delete this._inputEvent}},{key:"_onChange",value:function(){this.value!==this.el.value&&this.updateValue(),this.masked.doCommit(),this.updateControl(),this._saveSelection()}},{key:"_onDrop",value:function(t){t.preventDefault(),t.stopPropagation()}},{key:"_onFocus",value:function(t){this.alignCursorFriendly()}},{key:"_onClick",value:function(t){this.alignCursorFriendly()}},{key:"destroy",value:function(){this._unbindEvents(),this._listeners.length=0,delete this.el}}]),t}();iu.InputMask=Cu;var Eu=function(t){Pr(n,t);var e=Vr(n);function n(){return xr(this,n),e.apply(this,arguments)}return Or(n,[{key:"_update",value:function(t){t.enum&&(t.mask="*".repeat(t.enum[0].length)),Nr(Tr(n.prototype),"_update",this).call(this,t)}},{key:"doValidate",value:function(){for(var t,e=this,i=arguments.length,r=new Array(i),u=0;u<i;u++)r[u]=arguments[u];return this.enum.some((function(t){return t.indexOf(e.unmaskedValue)>=0}))&&(t=Nr(Tr(n.prototype),"doValidate",this)).call.apply(t,[this].concat(r))}}]),n}(gu);iu.MaskedEnum=Eu;var Fu=function(t){Pr(n,t);var e=Vr(n);function n(t){return xr(this,n),e.call(this,Object.assign({},n.DEFAULTS,t))}return Or(n,[{key:"_update",value:function(t){Nr(Tr(n.prototype),"_update",this).call(this,t),this._updateRegExps()}},{key:"_updateRegExps",value:function(){var t="^"+(this.allowNegative?"[+|\\-]?":""),e=(this.scale?"("+Jr(this.radix)+"\\d{0,"+this.scale+"})?":"")+"$";this._numberRegExpInput=new RegExp(t+"(0|([1-9]+\\d*))?"+e),this._numberRegExp=new RegExp(t+"\\d*"+e),this._mapToRadixRegExp=new RegExp("["+this.mapToRadix.map(Jr).join("")+"]","g"),this._thousandsSeparatorRegExp=new RegExp(Jr(this.thousandsSeparator),"g")}},{key:"_removeThousandsSeparators",value:function(t){return t.replace(this._thousandsSeparatorRegExp,"")}},{key:"_insertThousandsSeparators",value:function(t){var e=t.split(this.radix);return e[0]=e[0].replace(/\B(?=(\d{3})+(?!\d))/g,this.thousandsSeparator),e.join(this.radix)}},{key:"doPrepare",value:function(t){var e;t=t.replace(this._mapToRadixRegExp,this.radix);for(var i=this._removeThousandsSeparators(t),r=arguments.length,u=new Array(r>1?r-1:0),s=1;s<r;s++)u[s-1]=arguments[s];var a=Qr((e=Nr(Tr(n.prototype),"doPrepare",this)).call.apply(e,[this,i].concat(u))),o=qr(a,2),l=o[0],h=o[1];return t&&!i&&(h.skip=!0),[l,h]}},{key:"_separatorsCount",value:function(t){for(var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=0,i=0;i<t;++i)this._value.indexOf(this.thousandsSeparator,i)===i&&(++n,e&&(t+=this.thousandsSeparator.length));return n}},{key:"_separatorsCountFromSlice",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this._value;return this._separatorsCount(this._removeThousandsSeparators(t).length,!0)}},{key:"extractInput",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.value.length,i=arguments.length>2?arguments[2]:void 0,r=this._adjustRangeWithSeparators(t,e),u=qr(r,2);return t=u[0],e=u[1],this._removeThousandsSeparators(Nr(Tr(n.prototype),"extractInput",this).call(this,t,e,i))}},{key:"_appendCharRaw",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!this.thousandsSeparator)return Nr(Tr(n.prototype),"_appendCharRaw",this).call(this,t,e);var i=e.tail&&e._beforeTailState?e._beforeTailState._value:this._value,r=this._separatorsCountFromSlice(i);this._value=this._removeThousandsSeparators(this.value);var u=Nr(Tr(n.prototype),"_appendCharRaw",this).call(this,t,e);this._value=this._insertThousandsSeparators(this._value);var s=e.tail&&e._beforeTailState?e._beforeTailState._value:this._value,a=this._separatorsCountFromSlice(s);return u.tailShift+=(a-r)*this.thousandsSeparator.length,u.skip=!u.rawInserted&&t===this.thousandsSeparator,u}},{key:"_findSeparatorAround",value:function(t){if(this.thousandsSeparator){var e=t-this.thousandsSeparator.length+1,n=this.value.indexOf(this.thousandsSeparator,e);if(n<=t)return n}return-1}},{key:"_adjustRangeWithSeparators",value:function(t,e){var n=this._findSeparatorAround(t);n>=0&&(t=n);var i=this._findSeparatorAround(e);return i>=0&&(e=i+this.thousandsSeparator.length),[t,e]}},{key:"remove",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.value.length,n=this._adjustRangeWithSeparators(t,e),i=qr(n,2);t=i[0],e=i[1];var r=this.value.slice(0,t),u=this.value.slice(e),s=this._separatorsCount(r.length);this._value=this._insertThousandsSeparators(this._removeThousandsSeparators(r+u));var a=this._separatorsCountFromSlice(r);return new Yr({tailShift:(a-s)*this.thousandsSeparator.length})}},{key:"nearestInputPos",value:function(t,e){if(!this.thousandsSeparator)return t;switch(e){case Gr:case Kr:case $r:var n=this._findSeparatorAround(t-1);if(n>=0){var i=n+this.thousandsSeparator.length;if(t<i||this.value.length<=i||e===$r)return n}break;case Wr:case Xr:var r=this._findSeparatorAround(t);if(r>=0)return r+this.thousandsSeparator.length}return t}},{key:"doValidate",value:function(t){var e=(t.input?this._numberRegExpInput:this._numberRegExp).test(this._removeThousandsSeparators(this.value));if(e){var i=this.number;e=e&&!isNaN(i)&&(null==this.min||this.min>=0||this.min<=this.number)&&(null==this.max||this.max<=0||this.number<=this.max)}return e&&Nr(Tr(n.prototype),"doValidate",this).call(this,t)}},{key:"doCommit",value:function(){if(this.value){var t=this.number,e=t;null!=this.min&&(e=Math.max(e,this.min)),null!=this.max&&(e=Math.min(e,this.max)),e!==t&&(this.unmaskedValue=String(e));var i=this.value;this.normalizeZeros&&(i=this._normalizeZeros(i)),this.padFractionalZeros&&this.scale>0&&(i=this._padFractionalZeros(i)),this._value=i}Nr(Tr(n.prototype),"doCommit",this).call(this)}},{key:"_normalizeZeros",value:function(t){var e=this._removeThousandsSeparators(t).split(this.radix);return e[0]=e[0].replace(/^(\D*)(0*)(\d*)/,(function(t,e,n,i){return e+i})),t.length&&!/\d$/.test(e[0])&&(e[0]=e[0]+"0"),e.length>1&&(e[1]=e[1].replace(/0*$/,""),e[1].length||(e.length=1)),this._insertThousandsSeparators(e.join(this.radix))}},{key:"_padFractionalZeros",value:function(t){if(!t)return t;var e=t.split(this.radix);return e.length<2&&e.push(""),e[1]=e[1].padEnd(this.scale,"0"),e.join(this.radix)}},{key:"unmaskedValue",get:function(){return this._removeThousandsSeparators(this._normalizeZeros(this.value)).replace(this.radix,".")},set:function(t){zr(Tr(n.prototype),"unmaskedValue",t.replace(".",this.radix),this,!0)}},{key:"typedValue",get:function(){return Number(this.unmaskedValue)},set:function(t){zr(Tr(n.prototype),"unmaskedValue",String(t),this,!0)}},{key:"number",get:function(){return this.typedValue},set:function(t){this.typedValue=t}},{key:"allowNegative",get:function(){return this.signed||null!=this.min&&this.min<0||null!=this.max&&this.max<0}}]),n}(ru);Fu.DEFAULTS={radix:",",thousandsSeparator:"",mapToRadix:["."],scale:2,signed:!1,normalizeZeros:!0,padFractionalZeros:!1},iu.MaskedNumber=Fu;var Su=function(t){Pr(n,t);var e=Vr(n);function n(){return xr(this,n),e.apply(this,arguments)}return Or(n,[{key:"_update",value:function(t){t.mask&&(t.validate=t.mask),Nr(Tr(n.prototype),"_update",this).call(this,t)}}]),n}(ru);iu.MaskedFunction=Su;var wu=["compiledMasks","currentMaskRef","currentMask"],Bu=function(t){Pr(n,t);var e=Vr(n);function n(t){var i;return xr(this,n),(i=e.call(this,Object.assign({},n.DEFAULTS,t))).currentMask=null,i}return Or(n,[{key:"_update",value:function(t){Nr(Tr(n.prototype),"_update",this).call(this,t),"mask"in t&&(this.compiledMasks=Array.isArray(t.mask)?t.mask.map((function(t){return su(t)})):[])}},{key:"_appendCharRaw",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=this._applyDispatch(t,e);return this.currentMask&&n.aggregate(this.currentMask._appendChar(t,e)),n}},{key:"_applyDispatch",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.tail&&null!=e._beforeTailState?e._beforeTailState._value:this.value,i=this.rawInputValue,r=e.tail&&null!=e._beforeTailState?e._beforeTailState._rawInputValue:i,u=i.slice(r.length),s=this.currentMask,a=new Yr,o=s&&s.state;if(this.currentMask=this.doDispatch(t,Object.assign({},e)),this.currentMask)if(this.currentMask!==s){if(this.currentMask.reset(),r){var l=this.currentMask.append(r,{raw:!0});a.tailShift=l.inserted.length-n.length}u&&(a.tailShift+=this.currentMask.append(u,{raw:!0,tail:!0}).tailShift)}else this.currentMask.state=o;return a}},{key:"_appendPlaceholder",value:function(){var t=this._applyDispatch.apply(this,arguments);return this.currentMask&&t.aggregate(this.currentMask._appendPlaceholder()),t}},{key:"_appendEager",value:function(){var t=this._applyDispatch.apply(this,arguments);return this.currentMask&&t.aggregate(this.currentMask._appendEager()),t}},{key:"doDispatch",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.dispatch(t,this,e)}},{key:"doValidate",value:function(){for(var t,e,i=arguments.length,r=new Array(i),u=0;u<i;u++)r[u]=arguments[u];return(t=Nr(Tr(n.prototype),"doValidate",this)).call.apply(t,[this].concat(r))&&(!this.currentMask||(e=this.currentMask).doValidate.apply(e,r))}},{key:"reset",value:function(){var t;null===(t=this.currentMask)||void 0===t||t.reset(),this.compiledMasks.forEach((function(t){return t.reset()}))}},{key:"value",get:function(){return this.currentMask?this.currentMask.value:""},set:function(t){zr(Tr(n.prototype),"value",t,this,!0)}},{key:"unmaskedValue",get:function(){return this.currentMask?this.currentMask.unmaskedValue:""},set:function(t){zr(Tr(n.prototype),"unmaskedValue",t,this,!0)}},{key:"typedValue",get:function(){return this.currentMask?this.currentMask.typedValue:""},set:function(t){var e=String(t);this.currentMask&&(this.currentMask.typedValue=t,e=this.currentMask.unmaskedValue),this.unmaskedValue=e}},{key:"isComplete",get:function(){var t;return Boolean(null===(t=this.currentMask)||void 0===t?void 0:t.isComplete)}},{key:"isFilled",get:function(){var t;return Boolean(null===(t=this.currentMask)||void 0===t?void 0:t.isFilled)}},{key:"remove",value:function(){var t,e=new Yr;this.currentMask&&e.aggregate((t=this.currentMask).remove.apply(t,arguments)).aggregate(this._applyDispatch());return e}},{key:"state",get:function(){return Object.assign({},Nr(Tr(n.prototype),"state",this),{_rawInputValue:this.rawInputValue,compiledMasks:this.compiledMasks.map((function(t){return t.state})),currentMaskRef:this.currentMask,currentMask:this.currentMask&&this.currentMask.state})},set:function(t){var e=t.compiledMasks,i=t.currentMaskRef,r=t.currentMask,u=jr(t,wu);this.compiledMasks.forEach((function(t,n){return t.state=e[n]})),null!=i&&(this.currentMask=i,this.currentMask.state=r),zr(Tr(n.prototype),"state",u,this,!0)}},{key:"extractInput",value:function(){var t;return this.currentMask?(t=this.currentMask).extractInput.apply(t,arguments):""}},{key:"extractTail",value:function(){for(var t,e,i=arguments.length,r=new Array(i),u=0;u<i;u++)r[u]=arguments[u];return this.currentMask?(t=this.currentMask).extractTail.apply(t,r):(e=Nr(Tr(n.prototype),"extractTail",this)).call.apply(e,[this].concat(r))}},{key:"doCommit",value:function(){this.currentMask&&this.currentMask.doCommit(),Nr(Tr(n.prototype),"doCommit",this).call(this)}},{key:"nearestInputPos",value:function(){for(var t,e,i=arguments.length,r=new Array(i),u=0;u<i;u++)r[u]=arguments[u];return this.currentMask?(t=this.currentMask).nearestInputPos.apply(t,r):(e=Nr(Tr(n.prototype),"nearestInputPos",this)).call.apply(e,[this].concat(r))}},{key:"overwrite",get:function(){return this.currentMask?this.currentMask.overwrite:Nr(Tr(n.prototype),"overwrite",this)},set:function(t){console.warn('"overwrite" option is not available in dynamic mask, use this option in siblings')}},{key:"eager",get:function(){return this.currentMask?this.currentMask.eager:Nr(Tr(n.prototype),"eager",this)},set:function(t){console.warn('"eager" option is not available in dynamic mask, use this option in siblings')}},{key:"maskEquals",value:function(t){return Array.isArray(t)&&this.compiledMasks.every((function(e,n){var i;return e.maskEquals(null===(i=t[n])||void 0===i?void 0:i.mask)}))}}]),n}(ru);Bu.DEFAULTS={dispatch:function(t,e,n){if(e.compiledMasks.length){var i=e.rawInputValue,r=e.compiledMasks.map((function(e,r){return e.reset(),e.append(i,{raw:!0}),e.append(t,n),{weight:e.rawInputValue.length,index:r}}));return r.sort((function(t,e){return e.weight-t.weight})),e.compiledMasks[r[0].index]}}},iu.MaskedDynamic=Bu;var Du={MASKED:"value",UNMASKED:"unmaskedValue",TYPED:"typedValue"};function xu(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Du.MASKED,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Du.MASKED,i=su(t);return function(t){return i.runIsolated((function(i){return i[e]=t,i[n]}))}}function Mu(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),i=1;i<e;i++)n[i-1]=arguments[i];return xu.apply(void 0,n)(t)}iu.PIPE_TYPE=Du,iu.createPipe=xu,iu.pipe=Mu;try{globalThis.IMask=iu}catch(t){}t.HTMLContenteditableMaskElement=_u,t.HTMLMaskElement=bu,t.InputMask=Cu,t.MaskElement=mu,t.Masked=ru,t.MaskedDate=yu,t.MaskedDynamic=Bu,t.MaskedEnum=Eu,t.MaskedFunction=Su,t.MaskedNumber=Fu,t.MaskedPattern=gu,t.MaskedRange=ku,t.MaskedRegExp=du,t.PIPE_TYPE=Du,t.createMask=su,t.createPipe=xu,t.default=iu,t.pipe=Mu,Object.defineProperty(t,"__esModule",{value:!0})}));
//# sourceMappingURL=imask.min.js.map
