<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Financial Dashboard Accordion</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>

        .financial-accordion {
            max-width: 100%;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .accordion-header {
            padding: 20px 25px;
            border-bottom: 1px solid #e9ecef;
            cursor: pointer;
            position: relative;
        }
        
        .accordion-header:hover {
            background-color: #f8f9fa;
        }
        
        .chevron {
            position: absolute;
            right: 25px;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.3s ease;
            color: #6c757d;
        }
        
        .chevron.rotated {
            transform: translateY(-50%) rotate(180deg);
        }
        
        .accordion-content {
            padding: 25px;
            display: none;
        }
        
        .accordion-content.show {
            display: block;
        }
        
        .financial-row {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 60px;
        }
        
        .financial-section {
            flex: 1;
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .amount-large {
            font-size: 16px;
            font-weight: 500;
            color: #666;
        }
        
        .period-text {
            font-size: 14px;
            color: #999;
        }
        
        /* <CHANGE> Updated main amount styling and positioning for left section */
        .main-amount {
            font-size: 32px;
            font-weight: 700;
            color: #333;
            margin-bottom: 8px;
            line-height: 1;
        }
        
        .status-text {
            font-size: 14px;
            color: #666;
            margin-bottom: 20px;
        }
        
        /* <CHANGE> Made progress bars much taller and improved alignment */
        .progress-container {
            display: flex;
            gap: 0;
            height: 20px;
            border-radius: 10px;
            overflow: hidden;
            background-color: #e9ecef;
            margin-top: 10px;
        }
        
        .progress-bar-orange {
            background-color: #ff6b35;
            flex: 0 0 30%;
        }
        
        .progress-bar-gray {
            background-color: #e0e0e0;
            flex: 1;
        }
        
        .progress-bar-light-green {
            background-color: #4CAF50;
            flex: 0 0 55%;
        }
        
        .progress-bar-dark-green {
            background-color: #2E7D32;
            flex: 0 0 45%;
        }
        
        /* <CHANGE> Improved right section layout to match image exactly */
        .amounts-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        
        .amount-item {
            flex: 1;
        }
        
        .amount-item:first-child {
            text-align: left;
        }
        
        .amount-item:last-child {
            text-align: right;
        }
        
        .amount-value {
            font-size: 32px;
            font-weight: 700;
            color: #333;
            margin-bottom: 8px;
            line-height: 1;
        }
        
        .amount-label {
            font-size: 14px;
            color: #666;
        }
        
        /* <CHANGE> Added specific styling for right section progress container */
        .right-section .progress-container {
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div>
        <div class="financial-accordion">
            <div class="accordion-header" onclick="toggleAccordion()">
                <h5 class="mb-0">Payment Overview</h5>
                <i class="fas fa-chevron-up chevron" id="chevron"></i>
            </div>
            
            <div class="accordion-content show" id="accordionContent">
                <div class="financial-row">
                    <!-- Left Section - Unpaid -->
                    <div class="financial-section">
                        <div class="section-header">
                            <span class="amount-large">$5,281.52 Unpaid</span>
                            <span class="period-text">Last 365 days</span>
                        </div>
                        
                        <div class="main-amount">$1,525.50</div>
                        <div class="status-text">Overdue</div>
                        
                        <div class="progress-container">
                            <div class="progress-bar-orange"></div>
                            <div class="progress-bar-gray"></div>
                        </div>
                    </div>
                    
                    <!-- <CHANGE> Added right-section class and restructured layout -->
                    <!-- Right Section - Paid -->
                    <div class="financial-section right-section">
                        <div class="section-header">
                            <span class="amount-large">$3,692.22 Paid</span>
                            <span class="period-text">Last 30 days</span>
                        </div>
                        
                        <div class="amounts-row">
                            <div class="amount-item">
                                <div class="amount-value">$2,062.52</div>
                                <div class="amount-label">Not deposited</div>
                            </div>
                            <div class="amount-item">
                                <div class="amount-value">$1,629.70</div>
                                <div class="amount-label">Deposited</div>
                            </div>
                        </div>
                        
                        <div class="progress-container">
                            <div class="progress-bar-light-green"></div>
                            <div class="progress-bar-dark-green"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- ... existing code ... -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function toggleAccordion() {
            const content = document.getElementById('accordionContent');
            const chevron = document.getElementById('chevron');
            
            if (content.classList.contains('show')) {
                content.classList.remove('show');
                chevron.classList.add('rotated');
            } else {
                content.classList.add('show');
                chevron.classList.remove('rotated');
            }
        }
    </script>
</body>
</html>